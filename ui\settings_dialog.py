"""
Settings dialog for SIMAVER application.

This module provides a comprehensive settings dialog for configuring
application preferences, paths, and behavior.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import logging
from pathlib import Path
from typing import Dict, Any

from config.settings import settings


class SettingsDialog:
    """
    Settings configuration dialog.
    
    Provides a tabbed interface for configuring all application settings
    including general preferences, paths, monitoring, and UI options.
    """
    
    def __init__(self, parent):
        """
        Initialize settings dialog.
        
        Args:
            parent: Parent widget
        """
        self.logger = logging.getLogger(__name__)
        self.result = False  # True if settings were saved
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("SIMAVER Settings")
        self.dialog.geometry("600x500")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        self.dialog.resizable(False, False)
        
        # Variables for settings
        self.settings_vars = {}
        
        # Setup UI
        self._setup_ui()
        self._load_settings()
        
        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        # Configure grid
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        
        # Create tabs
        self._create_general_tab()
        self._create_paths_tab()
        self._create_monitoring_tab()
        self._create_ui_tab()
        self._create_comparison_tab()
        
        # Create buttons
        self._create_buttons()
    
    def _create_general_tab(self) -> None:
        """Create general settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="General")
        
        # Auto backup settings
        backup_frame = ttk.LabelFrame(frame, text="Backup Settings")
        backup_frame.pack(fill="x", padx=10, pady=10)
        
        self.settings_vars['auto_backup'] = tk.BooleanVar()
        ttk.Checkbutton(
            backup_frame,
            text="Enable automatic backup",
            variable=self.settings_vars['auto_backup']
        ).pack(anchor="w", padx=10, pady=5)
        
        # Backup interval
        interval_frame = ttk.Frame(backup_frame)
        interval_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(interval_frame, text="Backup interval (minutes):").pack(side="left")
        self.settings_vars['backup_interval'] = tk.IntVar()
        ttk.Spinbox(
            interval_frame,
            from_=5,
            to=1440,
            width=10,
            textvariable=self.settings_vars['backup_interval']
        ).pack(side="right")
        
        # Max backups
        max_frame = ttk.Frame(backup_frame)
        max_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(max_frame, text="Maximum backups per project:").pack(side="left")
        self.settings_vars['max_backups'] = tk.IntVar()
        ttk.Spinbox(
            max_frame,
            from_=1,
            to=1000,
            width=10,
            textvariable=self.settings_vars['max_backups']
        ).pack(side="right")
        
        # Startup settings
        startup_frame = ttk.LabelFrame(frame, text="Startup Settings")
        startup_frame.pack(fill="x", padx=10, pady=10)
        
        self.settings_vars['startup_scan'] = tk.BooleanVar()
        ttk.Checkbutton(
            startup_frame,
            text="Scan for projects on startup",
            variable=self.settings_vars['startup_scan']
        ).pack(anchor="w", padx=10, pady=5)
        
        self.settings_vars['minimize_to_tray'] = tk.BooleanVar()
        ttk.Checkbutton(
            startup_frame,
            text="Minimize to system tray",
            variable=self.settings_vars['minimize_to_tray']
        ).pack(anchor="w", padx=10, pady=5)
        
        # Notifications
        notif_frame = ttk.LabelFrame(frame, text="Notifications")
        notif_frame.pack(fill="x", padx=10, pady=10)
        
        self.settings_vars['show_notifications'] = tk.BooleanVar()
        ttk.Checkbutton(
            notif_frame,
            text="Show desktop notifications",
            variable=self.settings_vars['show_notifications']
        ).pack(anchor="w", padx=10, pady=5)
    
    def _create_paths_tab(self) -> None:
        """Create paths settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Paths")
        
        # Simatic projects directory
        projects_frame = ttk.LabelFrame(frame, text="Default Directories")
        projects_frame.pack(fill="x", padx=10, pady=10)
        
        # Projects directory
        proj_frame = ttk.Frame(projects_frame)
        proj_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(proj_frame, text="Simatic Projects Directory:").pack(anchor="w")
        
        proj_path_frame = ttk.Frame(proj_frame)
        proj_path_frame.pack(fill="x", pady=2)
        
        self.settings_vars['simatic_projects_dir'] = tk.StringVar()
        ttk.Entry(
            proj_path_frame,
            textvariable=self.settings_vars['simatic_projects_dir']
        ).pack(side="left", fill="x", expand=True)
        
        ttk.Button(
            proj_path_frame,
            text="Browse...",
            command=lambda: self._browse_directory('simatic_projects_dir')
        ).pack(side="right", padx=(5, 0))
        
        # Backup directory
        backup_frame = ttk.Frame(projects_frame)
        backup_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(backup_frame, text="Backup Directory:").pack(anchor="w")
        
        backup_path_frame = ttk.Frame(backup_frame)
        backup_path_frame.pack(fill="x", pady=2)
        
        self.settings_vars['backup_dir'] = tk.StringVar()
        ttk.Entry(
            backup_path_frame,
            textvariable=self.settings_vars['backup_dir']
        ).pack(side="left", fill="x", expand=True)
        
        ttk.Button(
            backup_path_frame,
            text="Browse...",
            command=lambda: self._browse_directory('backup_dir')
        ).pack(side="right", padx=(5, 0))
        
        # Temp directory
        temp_frame = ttk.Frame(projects_frame)
        temp_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(temp_frame, text="Temporary Directory:").pack(anchor="w")
        
        temp_path_frame = ttk.Frame(temp_frame)
        temp_path_frame.pack(fill="x", pady=2)
        
        self.settings_vars['temp_dir'] = tk.StringVar()
        ttk.Entry(
            temp_path_frame,
            textvariable=self.settings_vars['temp_dir']
        ).pack(side="left", fill="x", expand=True)
        
        ttk.Button(
            temp_path_frame,
            text="Browse...",
            command=lambda: self._browse_directory('temp_dir')
        ).pack(side="right", padx=(5, 0))
    
    def _create_monitoring_tab(self) -> None:
        """Create monitoring settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Monitoring")
        
        # File monitoring settings
        monitor_frame = ttk.LabelFrame(frame, text="File Monitoring")
        monitor_frame.pack(fill="x", padx=10, pady=10)
        
        self.settings_vars['watch_subdirs'] = tk.BooleanVar()
        ttk.Checkbutton(
            monitor_frame,
            text="Monitor subdirectories",
            variable=self.settings_vars['watch_subdirs']
        ).pack(anchor="w", padx=10, pady=5)
        
        # File extensions
        ext_frame = ttk.Frame(monitor_frame)
        ext_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(ext_frame, text="Monitored file extensions:").pack(anchor="w")
        
        self.settings_vars['file_extensions'] = tk.StringVar()
        ext_entry = ttk.Entry(ext_frame, textvariable=self.settings_vars['file_extensions'])
        ext_entry.pack(fill="x", pady=2)
        
        ttk.Label(
            ext_frame,
            text="Separate extensions with commas (e.g., .s7p,.awl,.scl)",
            font=("TkDefaultFont", 8)
        ).pack(anchor="w")
        
        # Exclude patterns
        exclude_frame = ttk.Frame(monitor_frame)
        exclude_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(exclude_frame, text="Exclude patterns:").pack(anchor="w")
        
        self.settings_vars['exclude_patterns'] = tk.StringVar()
        exclude_entry = ttk.Entry(exclude_frame, textvariable=self.settings_vars['exclude_patterns'])
        exclude_entry.pack(fill="x", pady=2)
        
        ttk.Label(
            exclude_frame,
            text="Separate patterns with commas (e.g., *.tmp,*.bak,*~)",
            font=("TkDefaultFont", 8)
        ).pack(anchor="w")
    
    def _create_ui_tab(self) -> None:
        """Create UI settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Interface")
        
        # Appearance settings
        appearance_frame = ttk.LabelFrame(frame, text="Appearance")
        appearance_frame.pack(fill="x", padx=10, pady=10)
        
        # Theme
        theme_frame = ttk.Frame(appearance_frame)
        theme_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(theme_frame, text="Theme:").pack(side="left")
        self.settings_vars['theme'] = tk.StringVar()
        theme_combo = ttk.Combobox(
            theme_frame,
            textvariable=self.settings_vars['theme'],
            values=["default", "clam", "alt", "classic"],
            state="readonly"
        )
        theme_combo.pack(side="right")
        
        # Font size
        font_frame = ttk.Frame(appearance_frame)
        font_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(font_frame, text="Font size:").pack(side="left")
        self.settings_vars['font_size'] = tk.IntVar()
        ttk.Spinbox(
            font_frame,
            from_=8,
            to=16,
            width=10,
            textvariable=self.settings_vars['font_size']
        ).pack(side="right")
    
    def _create_comparison_tab(self) -> None:
        """Create comparison settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Comparison")
        
        # Diff settings
        diff_frame = ttk.LabelFrame(frame, text="File Comparison")
        diff_frame.pack(fill="x", padx=10, pady=10)
        
        self.settings_vars['ignore_whitespace'] = tk.BooleanVar()
        ttk.Checkbutton(
            diff_frame,
            text="Ignore whitespace differences",
            variable=self.settings_vars['ignore_whitespace']
        ).pack(anchor="w", padx=10, pady=5)
        
        self.settings_vars['ignore_case'] = tk.BooleanVar()
        ttk.Checkbutton(
            diff_frame,
            text="Ignore case differences",
            variable=self.settings_vars['ignore_case']
        ).pack(anchor="w", padx=10, pady=5)
        
        # Context lines
        context_frame = ttk.Frame(diff_frame)
        context_frame.pack(fill="x", padx=10, pady=5)
        
        ttk.Label(context_frame, text="Context lines in diff:").pack(side="left")
        self.settings_vars['context_lines'] = tk.IntVar()
        ttk.Spinbox(
            context_frame,
            from_=0,
            to=10,
            width=10,
            textvariable=self.settings_vars['context_lines']
        ).pack(side="right")
    
    def _create_buttons(self) -> None:
        """Create dialog buttons."""
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        # Reset to defaults button
        ttk.Button(
            button_frame,
            text="Reset to Defaults",
            command=self._reset_to_defaults
        ).pack(side="left")
        
        # Cancel button
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self._cancel
        ).pack(side="right", padx=(5, 0))
        
        # OK button
        ttk.Button(
            button_frame,
            text="OK",
            command=self._save_settings
        ).pack(side="right")
    
    def _load_settings(self) -> None:
        """Load current settings into dialog."""
        # General settings
        self.settings_vars['auto_backup'].set(settings.get('general', 'auto_backup', True))
        self.settings_vars['backup_interval'].set(settings.get('general', 'backup_interval', 30))
        self.settings_vars['max_backups'].set(settings.get('general', 'max_backups', 50))
        self.settings_vars['startup_scan'].set(settings.get('general', 'startup_scan', True))
        self.settings_vars['minimize_to_tray'].set(settings.get('general', 'minimize_to_tray', True))
        self.settings_vars['show_notifications'].set(settings.get('general', 'show_notifications', True))
        
        # Paths
        self.settings_vars['simatic_projects_dir'].set(settings.get('paths', 'simatic_projects_dir', ''))
        self.settings_vars['backup_dir'].set(settings.get('paths', 'backup_dir', ''))
        self.settings_vars['temp_dir'].set(settings.get('paths', 'temp_dir', ''))
        
        # Monitoring
        self.settings_vars['watch_subdirs'].set(settings.get('monitoring', 'watch_subdirs', True))
        
        # File extensions
        extensions = settings.get('monitoring', 'file_extensions', [])
        self.settings_vars['file_extensions'].set(','.join(extensions))
        
        # Exclude patterns
        patterns = settings.get('monitoring', 'exclude_patterns', [])
        self.settings_vars['exclude_patterns'].set(','.join(patterns))
        
        # UI
        self.settings_vars['theme'].set(settings.get('ui', 'theme', 'default'))
        self.settings_vars['font_size'].set(settings.get('ui', 'font_size', 10))
        
        # Comparison
        self.settings_vars['ignore_whitespace'].set(settings.get('comparison', 'ignore_whitespace', False))
        self.settings_vars['ignore_case'].set(settings.get('comparison', 'ignore_case', False))
        self.settings_vars['context_lines'].set(settings.get('comparison', 'context_lines', 3))
    
    def _save_settings(self) -> None:
        """Save settings and close dialog."""
        try:
            # General settings
            settings.set('general', 'auto_backup', self.settings_vars['auto_backup'].get())
            settings.set('general', 'backup_interval', self.settings_vars['backup_interval'].get())
            settings.set('general', 'max_backups', self.settings_vars['max_backups'].get())
            settings.set('general', 'startup_scan', self.settings_vars['startup_scan'].get())
            settings.set('general', 'minimize_to_tray', self.settings_vars['minimize_to_tray'].get())
            settings.set('general', 'show_notifications', self.settings_vars['show_notifications'].get())
            
            # Paths
            settings.set('paths', 'simatic_projects_dir', self.settings_vars['simatic_projects_dir'].get())
            settings.set('paths', 'backup_dir', self.settings_vars['backup_dir'].get())
            settings.set('paths', 'temp_dir', self.settings_vars['temp_dir'].get())
            
            # Monitoring
            settings.set('monitoring', 'watch_subdirs', self.settings_vars['watch_subdirs'].get())
            
            # File extensions
            extensions_str = self.settings_vars['file_extensions'].get()
            extensions = [ext.strip() for ext in extensions_str.split(',') if ext.strip()]
            settings.set('monitoring', 'file_extensions', extensions)
            
            # Exclude patterns
            patterns_str = self.settings_vars['exclude_patterns'].get()
            patterns = [pattern.strip() for pattern in patterns_str.split(',') if pattern.strip()]
            settings.set('monitoring', 'exclude_patterns', patterns)
            
            # UI
            settings.set('ui', 'theme', self.settings_vars['theme'].get())
            settings.set('ui', 'font_size', self.settings_vars['font_size'].get())
            
            # Comparison
            settings.set('comparison', 'ignore_whitespace', self.settings_vars['ignore_whitespace'].get())
            settings.set('comparison', 'ignore_case', self.settings_vars['ignore_case'].get())
            settings.set('comparison', 'context_lines', self.settings_vars['context_lines'].get())
            
            # Save to file
            settings.save_settings()
            
            self.result = True
            self.dialog.destroy()
            
            messagebox.showinfo("Settings Saved", "Settings have been saved successfully.")
        
        except Exception as e:
            self.logger.error(f"Error saving settings: {e}")
            messagebox.showerror("Save Error", f"Failed to save settings:\n{e}")
    
    def _cancel(self) -> None:
        """Cancel dialog without saving."""
        self.result = False
        self.dialog.destroy()
    
    def _reset_to_defaults(self) -> None:
        """Reset all settings to defaults."""
        result = messagebox.askyesno(
            "Reset Settings",
            "Reset all settings to default values?\n\nThis action cannot be undone."
        )
        
        if result:
            settings.reset_to_defaults()
            self._load_settings()
            messagebox.showinfo("Reset Complete", "Settings have been reset to defaults.")
    
    def _browse_directory(self, setting_name: str) -> None:
        """
        Browse for directory.
        
        Args:
            setting_name: Name of the setting variable to update
        """
        directory = filedialog.askdirectory(
            title=f"Select Directory for {setting_name.replace('_', ' ').title()}"
        )
        
        if directory:
            self.settings_vars[setting_name].set(directory)
