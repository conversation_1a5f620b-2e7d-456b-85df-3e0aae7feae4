"""
Backup scheduler UI for SIMAVER application.

This module provides the backup management interface with backup list,
scheduling options, and backup operations.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import logging
from typing import Optional, List
from datetime import datetime

from logic.version_control import VersionControl
from logic.backup_manager import BackupInfo


class BackupSchedulerFrame(ttk.Frame):
    """
    Backup scheduler and management interface.
    
    Provides comprehensive backup management with scheduling,
    history, and restore operations.
    """
    
    def __init__(self, parent, version_control: VersionControl):
        """
        Initialize backup scheduler frame.
        
        Args:
            parent: Parent widget
            version_control: VersionControl instance
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.version_control = version_control
        
        # Setup UI
        self._setup_ui()
        
        # Load initial data
        self.refresh()
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        # Configure grid
        self.columnconfigure(0, weight=1)
        self.rowconfigure(1, weight=1)
        
        # Create toolbar
        self._create_toolbar()
        
        # Create backup list
        self._create_backup_list()
        
        # Create details panel
        self._create_details_panel()
    
    def _create_toolbar(self) -> None:
        """Create backup toolbar."""
        toolbar = ttk.Frame(self)
        toolbar.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # Create backup button
        ttk.Button(
            toolbar,
            text="Create Backup",
            command=self.create_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Restore backup button
        ttk.Button(
            toolbar,
            text="Restore Backup",
            command=self.restore_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Delete backup button
        ttk.Button(
            toolbar,
            text="Delete Backup",
            command=self.delete_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Cleanup button
        ttk.Button(
            toolbar,
            text="Cleanup Old",
            command=self.cleanup_old_backups
        ).pack(side=tk.LEFT, padx=2)
        
        # Export button
        ttk.Button(
            toolbar,
            text="Export List",
            command=self.export_backup_list
        ).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Filter frame
        filter_frame = ttk.Frame(toolbar)
        filter_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Label(filter_frame, text="Project:").pack(side=tk.LEFT)
        self.project_filter = ttk.Combobox(filter_frame, width=15, state="readonly")
        self.project_filter.pack(side=tk.LEFT, padx=5)
        self.project_filter.bind("<<ComboboxSelected>>", self.on_filter_change)
        
        # Refresh button
        ttk.Button(
            toolbar,
            text="Refresh",
            command=self.refresh
        ).pack(side=tk.RIGHT, padx=2)
    
    def _create_backup_list(self) -> None:
        """Create backup list with treeview."""
        # Create frame for backup list
        list_frame = ttk.LabelFrame(self, text="Backups")
        list_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Create treeview
        columns = ("backup_id", "project", "timestamp", "size", "files", "description")
        self.backup_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # Configure columns
        self.backup_tree.heading("backup_id", text="Backup ID")
        self.backup_tree.heading("project", text="Project")
        self.backup_tree.heading("timestamp", text="Created")
        self.backup_tree.heading("size", text="Size")
        self.backup_tree.heading("files", text="Files")
        self.backup_tree.heading("description", text="Description")
        
        # Configure column widths
        self.backup_tree.column("backup_id", width=200)
        self.backup_tree.column("project", width=150)
        self.backup_tree.column("timestamp", width=150)
        self.backup_tree.column("size", width=100)
        self.backup_tree.column("files", width=80)
        self.backup_tree.column("description", width=200)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.backup_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.backup_tree.xview)
        self.backup_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.backup_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Bind events
        self.backup_tree.bind("<<TreeviewSelect>>", self.on_backup_select)
        self.backup_tree.bind("<Double-1>", self.on_backup_double_click)
    
    def _create_details_panel(self) -> None:
        """Create backup details panel."""
        # Create frame for details
        details_frame = ttk.LabelFrame(self, text="Backup Details")
        details_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=5)
        details_frame.columnconfigure(1, weight=1)
        
        # Backup info
        ttk.Label(details_frame, text="Backup ID:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.id_label = ttk.Label(details_frame, text="")
        self.id_label.grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Project:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.project_label = ttk.Label(details_frame, text="")
        self.project_label.grid(row=1, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Created:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.timestamp_label = ttk.Label(details_frame, text="")
        self.timestamp_label.grid(row=2, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Size:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.size_label = ttk.Label(details_frame, text="")
        self.size_label.grid(row=3, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Files:").grid(row=4, column=0, sticky="w", padx=5, pady=2)
        self.files_label = ttk.Label(details_frame, text="")
        self.files_label.grid(row=4, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Path:").grid(row=5, column=0, sticky="w", padx=5, pady=2)
        self.path_label = ttk.Label(details_frame, text="")
        self.path_label.grid(row=5, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Description:").grid(row=6, column=0, sticky="nw", padx=5, pady=2)
        self.description_text = tk.Text(details_frame, height=3, wrap=tk.WORD)
        self.description_text.grid(row=6, column=1, sticky="ew", padx=5, pady=2)
        
        # Checksum
        ttk.Label(details_frame, text="Checksum:").grid(row=7, column=0, sticky="w", padx=5, pady=2)
        self.checksum_label = ttk.Label(details_frame, text="", font=("Courier", 8))
        self.checksum_label.grid(row=7, column=1, sticky="w", padx=5, pady=2)
    
    def refresh(self) -> None:
        """Refresh backup list."""
        # Clear existing items
        for item in self.backup_tree.get_children():
            self.backup_tree.delete(item)
        
        # Update project filter
        self._update_project_filter()
        
        # Get selected project filter
        selected_project = self.project_filter.get()
        if selected_project == "All Projects":
            selected_project = None
        
        # Get backups
        backups = self.version_control.backup_manager.list_backups(selected_project)
        
        # Populate tree
        for backup in backups:
            self._add_backup_to_tree(backup)
        
        self.logger.debug(f"Refreshed backup list with {len(backups)} backups")
    
    def _update_project_filter(self) -> None:
        """Update project filter combobox."""
        # Get all monitored projects
        projects = list(self.version_control.monitored_projects.keys())
        projects.insert(0, "All Projects")
        
        current_value = self.project_filter.get()
        self.project_filter['values'] = projects
        
        # Restore selection if still valid
        if current_value in projects:
            self.project_filter.set(current_value)
        else:
            self.project_filter.set("All Projects")
    
    def _add_backup_to_tree(self, backup: BackupInfo) -> None:
        """Add backup to tree view."""
        # Format size
        size_mb = backup.size_bytes / (1024 * 1024)
        if size_mb < 1:
            size_text = f"{backup.size_bytes / 1024:.1f} KB"
        else:
            size_text = f"{size_mb:.1f} MB"
        
        # Format timestamp
        timestamp_text = backup.timestamp.strftime("%Y-%m-%d %H:%M:%S")
        
        # Truncate description
        description = backup.description or ""
        if len(description) > 50:
            description = description[:47] + "..."
        
        # Insert into tree
        item = self.backup_tree.insert("", tk.END, values=(
            backup.backup_id,
            backup.project_name,
            timestamp_text,
            size_text,
            backup.file_count,
            description
        ))
        
        # Store backup info as item data
        self.backup_tree.set(item, "backup_info", backup)
    
    def on_backup_select(self, event) -> None:
        """Handle backup selection."""
        selection = self.backup_tree.selection()
        if not selection:
            self._clear_details()
            return
        
        item = selection[0]
        backup_info = self.backup_tree.set(item, "backup_info")
        
        if backup_info:
            self._update_details(backup_info)
    
    def on_backup_double_click(self, event) -> None:
        """Handle backup double-click."""
        self.restore_backup()
    
    def on_filter_change(self, event) -> None:
        """Handle project filter change."""
        self.refresh()
    
    def _update_details(self, backup: BackupInfo) -> None:
        """Update details panel with backup info."""
        self.id_label.config(text=backup.backup_id)
        self.project_label.config(text=backup.project_name)
        self.timestamp_label.config(text=backup.timestamp.strftime("%Y-%m-%d %H:%M:%S"))
        
        # Format size
        size_mb = backup.size_bytes / (1024 * 1024)
        if size_mb < 1:
            size_text = f"{backup.size_bytes / 1024:.1f} KB ({backup.size_bytes:,} bytes)"
        else:
            size_text = f"{size_mb:.1f} MB ({backup.size_bytes:,} bytes)"
        self.size_label.config(text=size_text)
        
        self.files_label.config(text=f"{backup.file_count:,} files")
        self.path_label.config(text=backup.backup_path)
        
        # Description
        self.description_text.delete(1.0, tk.END)
        if backup.description:
            self.description_text.insert(1.0, backup.description)
        
        # Checksum
        self.checksum_label.config(text=backup.checksum)
    
    def _clear_details(self) -> None:
        """Clear details panel."""
        self.id_label.config(text="")
        self.project_label.config(text="")
        self.timestamp_label.config(text="")
        self.size_label.config(text="")
        self.files_label.config(text="")
        self.path_label.config(text="")
        self.description_text.delete(1.0, tk.END)
        self.checksum_label.config(text="")
    
    def _get_selected_backup(self) -> Optional[BackupInfo]:
        """Get currently selected backup."""
        selection = self.backup_tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        return self.backup_tree.set(item, "backup_info")
    
    def create_backup(self) -> None:
        """Create new backup."""
        # Show project selection dialog
        projects = list(self.version_control.monitored_projects.keys())
        if not projects:
            messagebox.showwarning("No Projects", "No projects are currently monitored.")
            return
        
        # Simple project selection (would be a proper dialog)
        project_name = projects[0]  # For now, just use first project
        
        description = simpledialog.askstring(
            "Create Backup",
            f"Enter description for backup of '{project_name}':",
            initialvalue="Manual backup"
        )
        
        if description is not None:
            backup_info = self.version_control.create_backup(project_name, description)
            if backup_info:
                messagebox.showinfo("Backup Created", f"Backup created: {backup_info.backup_id}")
                self.refresh()
            else:
                messagebox.showerror("Backup Failed", "Failed to create backup.")
    
    def restore_backup(self) -> None:
        """Restore selected backup."""
        backup = self._get_selected_backup()
        if not backup:
            messagebox.showwarning("No Selection", "Please select a backup to restore.")
            return
        
        result = messagebox.askyesno(
            "Restore Backup",
            f"Restore backup '{backup.backup_id}'?\n\n"
            f"Project: {backup.project_name}\n"
            f"Created: {backup.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            "This will overwrite current project files."
        )
        
        if result:
            success = self.version_control.restore_backup(backup)
            if success:
                messagebox.showinfo("Restore Complete", "Backup restored successfully.")
            else:
                messagebox.showerror("Restore Failed", "Failed to restore backup.")
    
    def delete_backup(self) -> None:
        """Delete selected backup."""
        backup = self._get_selected_backup()
        if not backup:
            messagebox.showwarning("No Selection", "Please select a backup to delete.")
            return
        
        result = messagebox.askyesno(
            "Delete Backup",
            f"Delete backup '{backup.backup_id}'?\n\n"
            f"Project: {backup.project_name}\n"
            f"Created: {backup.timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"Size: {backup.size_bytes / (1024*1024):.1f} MB\n\n"
            "This action cannot be undone."
        )
        
        if result:
            success = self.version_control.backup_manager.delete_backup(backup)
            if success:
                messagebox.showinfo("Backup Deleted", "Backup deleted successfully.")
                self.refresh()
            else:
                messagebox.showerror("Delete Failed", "Failed to delete backup.")
    
    def cleanup_old_backups(self) -> None:
        """Cleanup old backups."""
        # Show cleanup dialog (simplified)
        max_backups = simpledialog.askinteger(
            "Cleanup Old Backups",
            "Keep how many recent backups per project?",
            initialvalue=10,
            minvalue=1,
            maxvalue=100
        )
        
        if max_backups:
            total_deleted = 0
            for project_name in self.version_control.monitored_projects:
                deleted = self.version_control.backup_manager.cleanup_old_backups(
                    project_name, max_backups
                )
                total_deleted += deleted
            
            messagebox.showinfo(
                "Cleanup Complete",
                f"Deleted {total_deleted} old backups."
            )
            self.refresh()
    
    def export_backup_list(self) -> None:
        """Export backup list to file."""
        from tkinter import filedialog
        import json
        
        file_path = filedialog.asksaveasfilename(
            title="Export Backup List",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                # Get all backups
                backups = self.version_control.backup_manager.list_backups()
                
                # Convert to serializable format
                backup_data = [backup.to_dict() for backup in backups]
                
                # Write to file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(backup_data, f, indent=2, ensure_ascii=False)
                
                messagebox.showinfo("Export Complete", f"Backup list exported to:\n{file_path}")
            
            except Exception as e:
                messagebox.showerror("Export Failed", f"Failed to export backup list:\n{e}")
