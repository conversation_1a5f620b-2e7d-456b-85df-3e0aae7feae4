# Styles Directory

This directory contains style files and themes for the SIMAVER application.

## Style Files

- default.tcl - Default tkinter theme customizations
- dark.tcl - Dark theme (optional)
- colors.py - Color definitions and palettes
- fonts.py - Font configurations

## Customization

The application uses tkinter's themed widgets (ttk) which can be customized
using TCL/Tk theme files or Python-based style configurations.

## Color Scheme

Default color scheme follows Windows 10/11 design guidelines:
- Primary: #0078D4 (Windows Blue)
- Success: #107C10 (Green)
- Warning: #FF8C00 (Orange)
- Error: #D13438 (Red)
- Background: #FFFFFF (White)
- Surface: #F3F2F1 (Light Gray)
- Text: #323130 (Dark Gray)
