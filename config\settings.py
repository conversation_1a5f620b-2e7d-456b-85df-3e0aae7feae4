"""
Configuration management for SIMAVER application.

This module handles application settings, user preferences, and configuration
persistence using JSON files and Windows registry when appropriate.
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import configparser


class Settings:
    """
    Manages application settings and configuration.
    
    Provides a centralized way to handle user preferences, application
    configuration, and persistent storage of settings.
    """
    
    def __init__(self, config_dir: Optional[str] = None):
        """
        Initialize settings manager.
        
        Args:
            config_dir: Optional custom configuration directory
        """
        self.logger = logging.getLogger(__name__)
        
        # Set up configuration directory
        if config_dir:
            self.config_dir = Path(config_dir)
        else:
            # Use AppData on Windows
            app_data = os.getenv('APPDATA', os.path.expanduser('~'))
            self.config_dir = Path(app_data) / 'SIMAVER'
        
        self.config_dir.mkdir(parents=True, exist_ok=True)
        self.config_file = self.config_dir / 'settings.json'
        
        # Default settings
        self.defaults = {
            'general': {
                'auto_backup': True,
                'backup_interval': 30,  # minutes
                'max_backups': 50,
                'startup_scan': True,
                'minimize_to_tray': True,
                'show_notifications': True
            },
            'paths': {
                'simatic_projects_dir': '',
                'backup_dir': str(self.config_dir / 'backups'),
                'temp_dir': str(self.config_dir / 'temp')
            },
            'ui': {
                'window_width': 1200,
                'window_height': 800,
                'window_x': 100,
                'window_y': 100,
                'theme': 'default',
                'font_size': 10
            },
            'monitoring': {
                'watch_subdirs': True,
                'file_extensions': ['.s7p', '.s7l', '.awl', '.scl', '.fbd', '.lad'],
                'exclude_patterns': ['*.tmp', '*.bak', '*~']
            },
            'comparison': {
                'ignore_whitespace': False,
                'ignore_case': False,
                'context_lines': 3
            }
        }
        
        self.settings = self.defaults.copy()
        self.load_settings()
    
    def load_settings(self) -> None:
        """Load settings from configuration file."""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_settings = json.load(f)
                    self._merge_settings(self.settings, loaded_settings)
                self.logger.info(f"Settings loaded from {self.config_file}")
            else:
                self.logger.info("No existing settings file found, using defaults")
                self.save_settings()  # Create default settings file
        except Exception as e:
            self.logger.error(f"Error loading settings: {e}")
            self.logger.info("Using default settings")
    
    def save_settings(self) -> None:
        """Save current settings to configuration file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Settings saved to {self.config_file}")
        except Exception as e:
            self.logger.error(f"Error saving settings: {e}")
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        Get a setting value.
        
        Args:
            section: Settings section name
            key: Setting key name
            default: Default value if setting not found
            
        Returns:
            Setting value or default
        """
        try:
            return self.settings.get(section, {}).get(key, default)
        except Exception:
            return default
    
    def set(self, section: str, key: str, value: Any) -> None:
        """
        Set a setting value.
        
        Args:
            section: Settings section name
            key: Setting key name
            value: Value to set
        """
        if section not in self.settings:
            self.settings[section] = {}
        self.settings[section][key] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get all settings from a section.
        
        Args:
            section: Section name
            
        Returns:
            Dictionary of section settings
        """
        return self.settings.get(section, {}).copy()
    
    def set_section(self, section: str, values: Dict[str, Any]) -> None:
        """
        Set multiple values in a section.
        
        Args:
            section: Section name
            values: Dictionary of key-value pairs to set
        """
        if section not in self.settings:
            self.settings[section] = {}
        self.settings[section].update(values)
    
    def reset_to_defaults(self) -> None:
        """Reset all settings to default values."""
        self.settings = self.defaults.copy()
        self.save_settings()
        self.logger.info("Settings reset to defaults")
    
    def _merge_settings(self, target: Dict, source: Dict) -> None:
        """
        Recursively merge settings dictionaries.
        
        Args:
            target: Target dictionary to merge into
            source: Source dictionary to merge from
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_settings(target[key], value)
            else:
                target[key] = value
    
    @property
    def backup_dir(self) -> Path:
        """Get backup directory path."""
        backup_path = Path(self.get('paths', 'backup_dir'))
        backup_path.mkdir(parents=True, exist_ok=True)
        return backup_path
    
    @property
    def temp_dir(self) -> Path:
        """Get temporary directory path."""
        temp_path = Path(self.get('paths', 'temp_dir'))
        temp_path.mkdir(parents=True, exist_ok=True)
        return temp_path


# Global settings instance
settings = Settings()
