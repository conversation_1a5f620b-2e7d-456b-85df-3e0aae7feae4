# SIMAVER - Simatic Manager Version Control

A comprehensive Windows desktop application for version control of Simatic Manager PLC programming projects. SIMAVER provides automated backup, change tracking, file monitoring, and restoration capabilities for STEP 7, TIA Portal, and other Simatic projects.

## Features

### Core Functionality
- **Automatic Project Detection**: Scans directories for Simatic projects (STEP 7, TIA Portal, STEP 5, WinCC)
- **Version Control**: Complete backup and restore functionality with metadata tracking
- **Real-time Monitoring**: File system monitoring with change detection and notifications
- **File Comparison**: Advanced diff engine with visual comparison tools
- **Backup Management**: Compressed and directory-based backups with cleanup policies
- **Scheduling**: Automatic backup scheduling with configurable intervals

### User Interface
- **Modern GUI**: Clean, intuitive interface built with tkinter
- **Project Manager**: Comprehensive project overview with status indicators
- **Backup Browser**: Easy backup browsing, comparison, and restoration
- **Diff Viewer**: Visual file comparison with syntax highlighting
- **Settings Dialog**: Extensive configuration options

### Windows Integration
- **System Tray**: Minimize to system tray with notifications
- **File Explorer**: Integration with Windows file explorer
- **Notifications**: Desktop notifications for important events
- **Auto-start**: Optional startup with Windows

## Installation

### Prerequisites
- Windows 10/11 (64-bit recommended)
- Python 3.12 or later
- Git (for development)

### Quick Installation

1. **Download and extract** the SIMAVER package to your desired location
2. **Run the installation script**:
   ```batch
   install.bat
   ```
3. **Start the application**:
   ```batch
   python main.py
   ```

### Manual Installation

1. **Clone or download** the repository:
   ```bash
   git clone <repository-url>
   cd SIMAVER
   ```

2. **Create a conda environment** (recommended):
   ```bash
   conda create -n simaver python=3.12
   conda activate simaver
   ```

3. **Install dependencies** (choose one method):

   **Method A - Full installation:**
   ```bash
   pip install -r requirements.txt
   ```

   **Method B - Minimal installation:**
   ```bash
   pip install -r requirements-minimal.txt
   ```

   **Method C - Essential only:**
   ```bash
   pip install watchdog psutil pillow
   ```

4. **Test basic functionality**:
   ```bash
   python test_basic.py
   ```

5. **Run the application**:
   ```bash
   python main.py
   ```

## Usage

### Getting Started

1. **Launch SIMAVER** using `python main.py`
2. **Scan for projects** using `File > Scan for Projects...`
3. **Select directory** containing your Simatic projects
4. **Enable monitoring** to track file changes automatically
5. **Configure settings** as needed via `Edit > Settings...`

### Project Management

- **Add Projects**: Use `File > Add Project...` or scan directories
- **Monitor Changes**: Enable file monitoring to track modifications
- **Create Backups**: Manual or automatic backup creation
- **View Status**: Real-time project status and change indicators

### Backup Operations

- **Create Backup**: Right-click project → "Create Backup"
- **Restore Backup**: Select backup → "Restore Backup"
- **Compare Versions**: Use diff viewer to see changes
- **Manage Backups**: Browse, delete, and organize backups

### File Monitoring

- **Real-time Tracking**: Monitors file changes in project directories
- **Change Detection**: Identifies added, modified, and deleted files
- **Notifications**: Desktop alerts for important changes
- **History**: Maintains change history for analysis

## Configuration

### Settings Categories

#### General Settings
- Auto backup enable/disable
- Backup interval (minutes)
- Maximum backups per project
- Startup behavior
- Notification preferences

#### Paths Configuration
- Default Simatic projects directory
- Backup storage location
- Temporary files directory

#### Monitoring Options
- File extensions to monitor
- Exclude patterns
- Subdirectory monitoring
- Change detection sensitivity

#### Comparison Settings
- Ignore whitespace differences
- Case sensitivity
- Context lines in diffs

### Configuration Files

Settings are stored in:
- **Windows**: `%APPDATA%\SIMAVER\settings.json`
- **Backup Database**: `%APPDATA%\SIMAVER\backups\backups.db`
- **Logs**: `%APPDATA%\SIMAVER\logs\simaver.log`

## Supported Project Types

### STEP 7 Projects
- File extensions: `.s7p`, `.s7l`
- Program files: `.awl`, `.scl`, `.fbd`, `.lad`
- Configuration files

### TIA Portal Projects
- File extensions: `.ap15`, `.ap16`, `.ap17`, `.ap18`
- Program files: `.scl`, `.awl`, `.fbd`, `.lad`
- Global data blocks

### STEP 5 Projects
- File extensions: `.s5d`
- Program files: `.awl`, `.s5`

### WinCC Projects
- File extensions: `.mcp`
- Configuration files: `.pdl`, `.fct`

## Architecture

### Project Structure
```
SIMAVER/
├── main.py                 # Application entry point
├── requirements.txt        # Python dependencies
├── install.bat            # Windows installation script
├── config/                # Configuration management
│   ├── __init__.py
│   └── settings.py
├── logic/                 # Business logic
│   ├── __init__.py
│   ├── version_control.py # Core version control
│   ├── backup_manager.py  # Backup operations
│   ├── file_monitor.py    # File system monitoring
│   ├── diff_engine.py     # File comparison
│   └── project_scanner.py # Project detection
├── ui/                    # User interface
│   ├── __init__.py
│   ├── main_window.py     # Main application window
│   ├── project_manager.py # Project management UI
│   ├── backup_scheduler.py# Backup management UI
│   ├── diff_viewer.py     # File comparison viewer
│   └── settings_dialog.py # Settings configuration
├── assets/                # Resources
│   ├── icons/            # Application icons
│   └── styles/           # UI themes and styles
└── tests/                # Unit tests
    ├── __init__.py
    ├── test_version_control.py
    ├── test_backup_manager.py
    └── test_diff_engine.py
```

### Key Components

- **Version Control**: Coordinates all operations
- **Backup Manager**: Handles backup creation and restoration
- **File Monitor**: Real-time file system monitoring
- **Diff Engine**: File and directory comparison
- **Project Scanner**: Automatic project detection
- **Settings Manager**: Configuration persistence

## Development

### Running Tests

```bash
# Run all tests
python -m pytest tests/

# Run specific test file
python -m pytest tests/test_version_control.py

# Run with coverage
python -m pytest tests/ --cov=logic --cov=config
```

### Code Style

The project follows PEP 8 style guidelines:
- Line length: 88 characters
- Docstrings: Google style
- Type hints: Encouraged
- Comments: Explain why, not what

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Ensure all tests pass
5. Submit a pull request

## Troubleshooting

### Common Issues

#### Installation Issues

**"No matching distribution found for sqlite3"**
- sqlite3 is built into Python, remove it from requirements if manually editing
- Use the corrected requirements.txt provided

**"Could not find a version that satisfies the requirement"**
- Try minimal installation: `pip install watchdog psutil pillow`
- Update pip: `pip install --upgrade pip`
- Use Python 3.8+ (3.12 recommended)

**Dependencies fail to install**
- Try installing one by one: `pip install watchdog`, `pip install psutil`, etc.
- Check if you're behind a corporate firewall
- Use `--trusted-host` flag if needed

#### Application Won't Start
- Check Python version (3.8+ required, 3.12+ recommended)
- Run basic test: `python test_basic.py`
- Check log files for error messages
- Verify tkinter is available: `python -c "import tkinter"`

#### File Monitoring Not Working
- Ensure sufficient permissions
- Check if antivirus is blocking file access
- Verify project paths are accessible
- Install watchdog: `pip install watchdog`

#### Backup Creation Fails
- Check available disk space
- Verify write permissions to backup directory
- Ensure project files are not locked

#### Performance Issues
- Reduce number of monitored projects
- Increase backup interval
- Exclude unnecessary file types

### Log Files

Check log files for detailed error information:
- Location: `%APPDATA%\SIMAVER\logs\simaver.log`
- Levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

### Support

For issues and questions:
1. Check the log files
2. Review this documentation
3. Search existing issues
4. Create a new issue with details

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Siemens for Simatic Manager and TIA Portal
- Python community for excellent libraries
- Contributors and testers

---

**SIMAVER** - Making PLC project version control simple and reliable.
