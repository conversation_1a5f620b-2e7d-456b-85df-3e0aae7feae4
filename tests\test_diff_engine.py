"""
Unit tests for diff engine functionality.

Tests file and directory comparison, diff generation, and change detection.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
import logging

# Disable logging during tests
logging.disable(logging.CRITICAL)

from logic.diff_engine import DiffEngine, ChangeType, FileDiff, DirectoryDiff


class TestDiffEngine(unittest.TestCase):
    """Test cases for DiffEngine class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for tests
        self.test_dir = Path(tempfile.mkdtemp())
        self.dir1 = self.test_dir / 'dir1'
        self.dir2 = self.test_dir / 'dir2'
        
        # Create test directories
        self.dir1.mkdir(parents=True)
        self.dir2.mkdir(parents=True)
        
        # Initialize diff engine
        self.diff_engine = DiffEngine()
    
    def tearDown(self):
        """Clean up test environment."""
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Re-enable logging
        logging.disable(logging.NOTSET)
    
    def test_compare_identical_files(self):
        """Test comparing identical files."""
        # Create identical files
        content = "Line 1\nLine 2\nLine 3\n"
        file1 = self.dir1 / 'test.txt'
        file2 = self.dir2 / 'test.txt'
        
        file1.write_text(content)
        file2.write_text(content)
        
        # Compare files
        diff = self.diff_engine.compare_files(file1, file2)
        
        # Verify result
        self.assertEqual(diff.change_type, ChangeType.UNCHANGED)
        self.assertEqual(diff.similarity, 1.0)
        self.assertEqual(diff.lines_added, 0)
        self.assertEqual(diff.lines_deleted, 0)
    
    def test_compare_modified_files(self):
        """Test comparing modified files."""
        # Create files with different content
        content1 = "Line 1\nLine 2\nLine 3\n"
        content2 = "Line 1\nModified Line 2\nLine 3\nNew Line 4\n"
        
        file1 = self.dir1 / 'test.txt'
        file2 = self.dir2 / 'test.txt'
        
        file1.write_text(content1)
        file2.write_text(content2)
        
        # Compare files
        diff = self.diff_engine.compare_files(file1, file2)
        
        # Verify result
        self.assertEqual(diff.change_type, ChangeType.MODIFIED)
        self.assertLess(diff.similarity, 1.0)
        self.assertGreater(diff.lines_added, 0)
        self.assertGreater(diff.lines_deleted, 0)
        self.assertIsNotNone(diff.diff_content)
    
    def test_compare_added_file(self):
        """Test comparing when file is added."""
        # Create file only in dir2
        file1 = self.dir1 / 'nonexistent.txt'
        file2 = self.dir2 / 'new.txt'
        file2.write_text("New file content\n")
        
        # Compare files
        diff = self.diff_engine.compare_files(file1, file2)
        
        # Verify result
        self.assertEqual(diff.change_type, ChangeType.ADDED)
        self.assertGreater(diff.lines_added, 0)
        self.assertEqual(diff.lines_deleted, 0)
    
    def test_compare_deleted_file(self):
        """Test comparing when file is deleted."""
        # Create file only in dir1
        file1 = self.dir1 / 'deleted.txt'
        file2 = self.dir2 / 'nonexistent.txt'
        file1.write_text("File to be deleted\n")
        
        # Compare files
        diff = self.diff_engine.compare_files(file1, file2)
        
        # Verify result
        self.assertEqual(diff.change_type, ChangeType.DELETED)
        self.assertEqual(diff.lines_added, 0)
        self.assertGreater(diff.lines_deleted, 0)
    
    def test_compare_binary_files(self):
        """Test comparing binary files."""
        # Create binary files (simulate with different content)
        file1 = self.dir1 / 'binary.s7p'
        file2 = self.dir2 / 'binary.s7p'
        
        file1.write_bytes(b'\x00\x01\x02\x03\x04')
        file2.write_bytes(b'\x00\x01\x02\x03\x05')  # Different last byte
        
        # Compare files
        diff = self.diff_engine.compare_files(file1, file2)
        
        # Verify result
        self.assertEqual(diff.change_type, ChangeType.MODIFIED)
        self.assertTrue(diff.binary_file)
        self.assertEqual(diff.similarity, 0.0)  # Binary files are either identical or different
    
    def test_compare_directories_with_changes(self):
        """Test comparing directories with various changes."""
        # Create files in dir1
        (self.dir1 / 'unchanged.txt').write_text("Same content")
        (self.dir1 / 'modified.txt').write_text("Original content")
        (self.dir1 / 'deleted.txt').write_text("Will be deleted")
        (self.dir1 / 'renamed_old.txt').write_text("Renamed file content")
        
        # Create files in dir2
        (self.dir2 / 'unchanged.txt').write_text("Same content")
        (self.dir2 / 'modified.txt').write_text("Modified content")
        (self.dir2 / 'added.txt').write_text("New file")
        (self.dir2 / 'renamed_new.txt').write_text("Renamed file content")
        
        # Compare directories
        diff = self.diff_engine.compare_directories(self.dir1, self.dir2)
        
        # Verify results
        self.assertIn('added.txt', diff.added_files)
        self.assertIn('deleted.txt', diff.deleted_files)
        self.assertIn('unchanged.txt', diff.unchanged_files)
        
        # Check modified files
        modified_names = [f.file_path for f in diff.modified_files]
        self.assertIn('modified.txt', modified_names)
        
        # Check renamed files (should detect the rename)
        self.assertGreater(len(diff.renamed_files), 0)
    
    def test_generate_unified_diff(self):
        """Test generating unified diff format."""
        # Create files with different content
        content1 = "Line 1\nLine 2\nLine 3\nLine 4\n"
        content2 = "Line 1\nModified Line 2\nLine 3\nLine 4\nNew Line 5\n"
        
        file1 = self.dir1 / 'test.txt'
        file2 = self.dir2 / 'test.txt'
        
        file1.write_text(content1)
        file2.write_text(content2)
        
        # Generate unified diff
        diff_text = self.diff_engine.generate_unified_diff(file1, file2)
        
        # Verify diff format
        self.assertIn('---', diff_text)  # From file marker
        self.assertIn('+++', diff_text)  # To file marker
        self.assertIn('@@', diff_text)   # Hunk header
        self.assertIn('-Line 2', diff_text)  # Deleted line
        self.assertIn('+Modified Line 2', diff_text)  # Added line
        self.assertIn('+New Line 5', diff_text)  # Added line
    
    def test_ignore_whitespace_option(self):
        """Test ignoring whitespace differences."""
        # Create files with whitespace differences
        content1 = "Line 1\nLine 2\nLine 3\n"
        content2 = "Line 1  \n  Line 2\nLine 3\n"  # Extra spaces
        
        file1 = self.dir1 / 'test.txt'
        file2 = self.dir2 / 'test.txt'
        
        file1.write_text(content1)
        file2.write_text(content2)
        
        # Compare without ignoring whitespace
        self.diff_engine.ignore_whitespace = False
        diff1 = self.diff_engine.compare_files(file1, file2)
        
        # Compare with ignoring whitespace
        self.diff_engine.ignore_whitespace = True
        diff2 = self.diff_engine.compare_files(file1, file2)
        
        # Verify results
        self.assertEqual(diff1.change_type, ChangeType.MODIFIED)
        self.assertEqual(diff2.change_type, ChangeType.UNCHANGED)
    
    def test_ignore_case_option(self):
        """Test ignoring case differences."""
        # Create files with case differences
        content1 = "Line 1\nLine 2\nLine 3\n"
        content2 = "line 1\nLINE 2\nLine 3\n"  # Different case
        
        file1 = self.dir1 / 'test.txt'
        file2 = self.dir2 / 'test.txt'
        
        file1.write_text(content1)
        file2.write_text(content2)
        
        # Compare without ignoring case
        self.diff_engine.ignore_case = False
        diff1 = self.diff_engine.compare_files(file1, file2)
        
        # Compare with ignoring case
        self.diff_engine.ignore_case = True
        diff2 = self.diff_engine.compare_files(file1, file2)
        
        # Verify results
        self.assertEqual(diff1.change_type, ChangeType.MODIFIED)
        self.assertEqual(diff2.change_type, ChangeType.UNCHANGED)
    
    def test_context_lines_setting(self):
        """Test context lines setting in diff."""
        # Create files with multiple lines
        lines1 = [f"Line {i}" for i in range(1, 11)]
        lines2 = lines1.copy()
        lines2[4] = "Modified Line 5"  # Change line 5
        
        file1 = self.dir1 / 'test.txt'
        file2 = self.dir2 / 'test.txt'
        
        file1.write_text('\n'.join(lines1) + '\n')
        file2.write_text('\n'.join(lines2) + '\n')
        
        # Test different context line settings
        for context_lines in [1, 3, 5]:
            self.diff_engine.context_lines = context_lines
            diff_text = self.diff_engine.generate_unified_diff(file1, file2)
            
            # Count context lines (this is a simplified check)
            lines = diff_text.split('\n')
            context_count = sum(1 for line in lines if line.startswith(' '))
            
            # Should have some context lines
            self.assertGreater(context_count, 0)
    
    def test_detect_renamed_files(self):
        """Test detection of renamed files."""
        # Create identical content in differently named files
        content = "Identical file content\nLine 2\nLine 3\n"
        
        (self.dir1 / 'old_name.txt').write_text(content)
        (self.dir2 / 'new_name.txt').write_text(content)
        
        # Compare directories
        diff = self.diff_engine.compare_directories(self.dir1, self.dir2)
        
        # Should detect the rename
        self.assertEqual(len(diff.renamed_files), 1)
        old_name, new_name = diff.renamed_files[0]
        self.assertEqual(old_name, 'old_name.txt')
        self.assertEqual(new_name, 'new_name.txt')
        
        # Files should not appear in added/deleted lists
        self.assertNotIn('old_name.txt', diff.deleted_files)
        self.assertNotIn('new_name.txt', diff.added_files)
    
    def test_file_type_detection(self):
        """Test file type detection (text vs binary)."""
        # Create text file
        text_file = self.dir1 / 'text.awl'
        text_file.write_text("AWL program content")
        
        # Create binary file (simulate)
        binary_file = self.dir1 / 'binary.s7p'
        binary_file.write_bytes(b'\x00\x01\x02\x03\x04')
        
        # Test detection
        self.assertFalse(self.diff_engine._is_binary_file(text_file))
        self.assertTrue(self.diff_engine._is_binary_file(binary_file))


if __name__ == '__main__':
    unittest.main()
