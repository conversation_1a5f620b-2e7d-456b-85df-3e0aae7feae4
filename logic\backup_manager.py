"""
Backup management for SIMAVER application.

This module handles creating, managing, and restoring backups of Simatic projects.
Provides versioned backup functionality with compression and metadata tracking.
"""

import os
import shutil
import logging
import sqlite3
import hashlib
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import json
import zipfile
import threading
from concurrent.futures import ThreadPoolExecutor

from .project_scanner import SimaticProject


@dataclass
class BackupInfo:
    """
    Information about a backup.
    
    Contains metadata about a specific backup version.
    """
    backup_id: str
    project_name: str
    project_path: str
    backup_path: str
    timestamp: datetime
    size_bytes: int
    file_count: int
    checksum: str
    description: Optional[str] = None
    tags: Optional[List[str]] = None
    
    def to_dict(self) -> Dict:
        """Convert backup info to dictionary."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class BackupManager:
    """
    Manages backup operations for Simatic projects.
    
    Provides functionality to create, list, restore, and manage backups
    with version control and metadata tracking.
    """
    
    def __init__(self, backup_root: Path):
        """
        Initialize backup manager.
        
        Args:
            backup_root: Root directory for storing backups
        """
        self.logger = logging.getLogger(__name__)
        self.backup_root = Path(backup_root)
        self.backup_root.mkdir(parents=True, exist_ok=True)
        
        # Database for backup metadata
        self.db_path = self.backup_root / 'backups.db'
        self._init_database()
        
        # Thread pool for async operations
        self.executor = ThreadPoolExecutor(max_workers=2)
        self._lock = threading.Lock()
    
    def _init_database(self) -> None:
        """Initialize SQLite database for backup metadata."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS backups (
                        backup_id TEXT PRIMARY KEY,
                        project_name TEXT NOT NULL,
                        project_path TEXT NOT NULL,
                        backup_path TEXT NOT NULL,
                        timestamp TEXT NOT NULL,
                        size_bytes INTEGER NOT NULL,
                        file_count INTEGER NOT NULL,
                        checksum TEXT NOT NULL,
                        description TEXT,
                        tags TEXT
                    )
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_project_name 
                    ON backups(project_name)
                ''')
                
                conn.execute('''
                    CREATE INDEX IF NOT EXISTS idx_timestamp 
                    ON backups(timestamp)
                ''')
                
                conn.commit()
                self.logger.info("Backup database initialized")
        
        except Exception as e:
            self.logger.error(f"Error initializing backup database: {e}")
            raise
    
    def create_backup(self, project: SimaticProject, 
                     description: Optional[str] = None,
                     tags: Optional[List[str]] = None,
                     compress: bool = True) -> BackupInfo:
        """
        Create a backup of a Simatic project.
        
        Args:
            project: SimaticProject to backup
            description: Optional description for the backup
            tags: Optional tags for categorizing the backup
            compress: Whether to compress the backup
            
        Returns:
            BackupInfo for the created backup
        """
        self.logger.info(f"Creating backup for project: {project.name}")
        
        # Generate backup ID and paths
        timestamp = datetime.now()
        backup_id = self._generate_backup_id(project.name, timestamp)
        
        project_backup_dir = self.backup_root / project.name
        project_backup_dir.mkdir(parents=True, exist_ok=True)
        
        if compress:
            backup_path = project_backup_dir / f"{backup_id}.zip"
            file_count, size_bytes, checksum = self._create_compressed_backup(
                project, backup_path
            )
        else:
            backup_path = project_backup_dir / backup_id
            file_count, size_bytes, checksum = self._create_directory_backup(
                project, backup_path
            )
        
        # Create backup info
        backup_info = BackupInfo(
            backup_id=backup_id,
            project_name=project.name,
            project_path=str(project.path),
            backup_path=str(backup_path),
            timestamp=timestamp,
            size_bytes=size_bytes,
            file_count=file_count,
            checksum=checksum,
            description=description,
            tags=tags or []
        )
        
        # Store in database
        self._store_backup_info(backup_info)
        
        self.logger.info(f"Backup created: {backup_id} ({file_count} files, {size_bytes} bytes)")
        return backup_info
    
    def _generate_backup_id(self, project_name: str, timestamp: datetime) -> str:
        """Generate unique backup ID."""
        time_str = timestamp.strftime("%Y%m%d_%H%M%S")
        return f"{project_name}_{time_str}"
    
    def _create_compressed_backup(self, project: SimaticProject, 
                                backup_path: Path) -> Tuple[int, int, str]:
        """
        Create compressed ZIP backup.
        
        Returns:
            Tuple of (file_count, total_size, checksum)
        """
        file_count = 0
        total_size = 0
        hash_md5 = hashlib.md5()
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add all project files
                for file_path in project.path.rglob('*'):
                    if file_path.is_file():
                        # Calculate relative path
                        rel_path = file_path.relative_to(project.path)
                        
                        # Add to ZIP
                        zipf.write(file_path, rel_path)
                        
                        # Update counters
                        file_count += 1
                        file_size = file_path.stat().st_size
                        total_size += file_size
                        
                        # Update checksum
                        with open(file_path, 'rb') as f:
                            for chunk in iter(lambda: f.read(4096), b""):
                                hash_md5.update(chunk)
                
                # Add metadata
                metadata = {
                    'project_name': project.name,
                    'project_type': project.project_type,
                    'original_path': str(project.path),
                    'backup_timestamp': datetime.now().isoformat(),
                    'file_count': file_count,
                    'total_size': total_size
                }
                
                zipf.writestr('backup_metadata.json', 
                            json.dumps(metadata, indent=2))
        
        except Exception as e:
            self.logger.error(f"Error creating compressed backup: {e}")
            if backup_path.exists():
                backup_path.unlink()
            raise
        
        return file_count, total_size, hash_md5.hexdigest()
    
    def _create_directory_backup(self, project: SimaticProject, 
                               backup_path: Path) -> Tuple[int, int, str]:
        """
        Create directory-based backup.
        
        Returns:
            Tuple of (file_count, total_size, checksum)
        """
        file_count = 0
        total_size = 0
        hash_md5 = hashlib.md5()
        
        try:
            backup_path.mkdir(parents=True, exist_ok=True)
            
            # Copy all project files
            for file_path in project.path.rglob('*'):
                if file_path.is_file():
                    # Calculate relative path and destination
                    rel_path = file_path.relative_to(project.path)
                    dest_path = backup_path / rel_path
                    
                    # Create destination directory
                    dest_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Copy file
                    shutil.copy2(file_path, dest_path)
                    
                    # Update counters
                    file_count += 1
                    file_size = file_path.stat().st_size
                    total_size += file_size
                    
                    # Update checksum
                    with open(file_path, 'rb') as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            hash_md5.update(chunk)
            
            # Create metadata file
            metadata = {
                'project_name': project.name,
                'project_type': project.project_type,
                'original_path': str(project.path),
                'backup_timestamp': datetime.now().isoformat(),
                'file_count': file_count,
                'total_size': total_size
            }
            
            with open(backup_path / 'backup_metadata.json', 'w') as f:
                json.dump(metadata, f, indent=2)
        
        except Exception as e:
            self.logger.error(f"Error creating directory backup: {e}")
            if backup_path.exists():
                shutil.rmtree(backup_path, ignore_errors=True)
            raise
        
        return file_count, total_size, hash_md5.hexdigest()
    
    def _store_backup_info(self, backup_info: BackupInfo) -> None:
        """Store backup information in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO backups 
                    (backup_id, project_name, project_path, backup_path, 
                     timestamp, size_bytes, file_count, checksum, description, tags)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    backup_info.backup_id,
                    backup_info.project_name,
                    backup_info.project_path,
                    backup_info.backup_path,
                    backup_info.timestamp.isoformat(),
                    backup_info.size_bytes,
                    backup_info.file_count,
                    backup_info.checksum,
                    backup_info.description,
                    json.dumps(backup_info.tags) if backup_info.tags else None
                ))
                conn.commit()
        
        except Exception as e:
            self.logger.error(f"Error storing backup info: {e}")
            raise
    
    def list_backups(self, project_name: Optional[str] = None) -> List[BackupInfo]:
        """
        List available backups.
        
        Args:
            project_name: Optional project name filter
            
        Returns:
            List of BackupInfo objects
        """
        backups = []
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                if project_name:
                    cursor = conn.execute('''
                        SELECT * FROM backups 
                        WHERE project_name = ? 
                        ORDER BY timestamp DESC
                    ''', (project_name,))
                else:
                    cursor = conn.execute('''
                        SELECT * FROM backups 
                        ORDER BY timestamp DESC
                    ''')
                
                for row in cursor.fetchall():
                    backup_info = BackupInfo(
                        backup_id=row[0],
                        project_name=row[1],
                        project_path=row[2],
                        backup_path=row[3],
                        timestamp=datetime.fromisoformat(row[4]),
                        size_bytes=row[5],
                        file_count=row[6],
                        checksum=row[7],
                        description=row[8],
                        tags=json.loads(row[9]) if row[9] else []
                    )
                    backups.append(backup_info)
        
        except Exception as e:
            self.logger.error(f"Error listing backups: {e}")
        
        return backups

    def restore_backup(self, backup_info: BackupInfo,
                      restore_path: Optional[Path] = None) -> bool:
        """
        Restore a backup to specified location.

        Args:
            backup_info: Backup to restore
            restore_path: Optional custom restore location

        Returns:
            True if restore successful
        """
        self.logger.info(f"Restoring backup: {backup_info.backup_id}")

        if restore_path is None:
            restore_path = Path(backup_info.project_path)

        backup_path = Path(backup_info.backup_path)

        try:
            if backup_path.suffix == '.zip':
                return self._restore_compressed_backup(backup_path, restore_path)
            else:
                return self._restore_directory_backup(backup_path, restore_path)

        except Exception as e:
            self.logger.error(f"Error restoring backup: {e}")
            return False

    def _restore_compressed_backup(self, backup_path: Path,
                                 restore_path: Path) -> bool:
        """Restore from compressed backup."""
        try:
            restore_path.mkdir(parents=True, exist_ok=True)

            with zipfile.ZipFile(backup_path, 'r') as zipf:
                zipf.extractall(restore_path)

            self.logger.info(f"Compressed backup restored to: {restore_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error restoring compressed backup: {e}")
            return False

    def _restore_directory_backup(self, backup_path: Path,
                                restore_path: Path) -> bool:
        """Restore from directory backup."""
        try:
            if restore_path.exists():
                shutil.rmtree(restore_path)

            shutil.copytree(backup_path, restore_path)

            self.logger.info(f"Directory backup restored to: {restore_path}")
            return True

        except Exception as e:
            self.logger.error(f"Error restoring directory backup: {e}")
            return False

    def delete_backup(self, backup_info: BackupInfo) -> bool:
        """
        Delete a backup.

        Args:
            backup_info: Backup to delete

        Returns:
            True if deletion successful
        """
        try:
            backup_path = Path(backup_info.backup_path)

            # Delete backup files
            if backup_path.exists():
                if backup_path.is_file():
                    backup_path.unlink()
                else:
                    shutil.rmtree(backup_path)

            # Remove from database
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('DELETE FROM backups WHERE backup_id = ?',
                           (backup_info.backup_id,))
                conn.commit()

            self.logger.info(f"Backup deleted: {backup_info.backup_id}")
            return True

        except Exception as e:
            self.logger.error(f"Error deleting backup: {e}")
            return False

    def cleanup_old_backups(self, project_name: str, max_backups: int) -> int:
        """
        Clean up old backups, keeping only the most recent ones.

        Args:
            project_name: Project name to clean up
            max_backups: Maximum number of backups to keep

        Returns:
            Number of backups deleted
        """
        backups = self.list_backups(project_name)

        if len(backups) <= max_backups:
            return 0

        # Sort by timestamp (newest first) and delete old ones
        backups.sort(key=lambda b: b.timestamp, reverse=True)
        old_backups = backups[max_backups:]

        deleted_count = 0
        for backup in old_backups:
            if self.delete_backup(backup):
                deleted_count += 1

        self.logger.info(f"Cleaned up {deleted_count} old backups for {project_name}")
        return deleted_count

    def get_backup_statistics(self) -> Dict:
        """Get backup statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT
                        COUNT(*) as total_backups,
                        COUNT(DISTINCT project_name) as unique_projects,
                        SUM(size_bytes) as total_size,
                        AVG(size_bytes) as avg_size,
                        MIN(timestamp) as oldest_backup,
                        MAX(timestamp) as newest_backup
                    FROM backups
                ''')

                row = cursor.fetchone()

                return {
                    'total_backups': row[0] or 0,
                    'unique_projects': row[1] or 0,
                    'total_size_bytes': row[2] or 0,
                    'average_size_bytes': row[3] or 0,
                    'oldest_backup': row[4],
                    'newest_backup': row[5]
                }

        except Exception as e:
            self.logger.error(f"Error getting backup statistics: {e}")
            return {}

    def close(self) -> None:
        """Clean up resources."""
        self.executor.shutdown(wait=True)
