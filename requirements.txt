# SIMAVER - Simatic Manager Version Control Application
# Dependencies for Windows Desktop GUI Application

# GUI Framework
tkinter-tooltip==2.1.0
pillow==10.1.0

# File Operations and Monitoring
watchdog==3.0.0
send2trash==1.8.2

# Data Processing and Storage
sqlite3  # Built-in with Python
json5==0.9.14
configparser  # Built-in with Python

# Date/Time Operations
python-dateutil==2.8.2

# Windows-specific features
plyer==2.1.0  # For notifications
pystray==0.19.4  # For system tray
psutil==5.9.6  # For system monitoring

# Logging and Utilities
colorlog==6.8.0

# Development and Testing
pytest==7.4.3
pytest-cov==4.1.0

# Optional: For advanced file comparison
difflib  # Built-in with Python

# Conda Environment Setup:
# To create a new conda environment with Python 3.12, run:
# conda create -n simaver python=3.12
# conda activate simaver
# pip install -r requirements.txt
