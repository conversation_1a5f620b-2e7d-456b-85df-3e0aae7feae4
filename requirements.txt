# SIMAVER - Simatic Manager Version Control Application
# Dependencies for Windows Desktop GUI Application

# File Operations and Monitoring
watchdog==3.0.0
send2trash==1.8.2

# Data Processing
python-dateutil==2.8.2

# Windows-specific features
plyer==2.1.0  # For notifications
pystray==0.19.4  # For system tray
psutil==5.9.6  # For system monitoring

# Image processing (for icons and UI)
pillow>=9.0.0

# Development and Testing
pytest==7.4.3
pytest-cov==4.1.0

# Note: The following modules are built into Python and don't need installation:
# - tkinter (GUI framework)
# - sqlite3 (database)
# - json (JSON processing)
# - configparser (configuration files)
# - difflib (file comparison)
# - logging (application logging)
# - threading (concurrent operations)
# - pathlib (path operations)
# - datetime (date/time operations)

# Conda Environment Setup:
# To create a new conda environment with Python 3.12, run:
# conda create -n simaver python=3.12
# conda activate simaver
# pip install -r requirements.txt
