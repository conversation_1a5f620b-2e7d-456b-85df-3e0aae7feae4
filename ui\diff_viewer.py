"""
Diff viewer dialog for SIMAVER application.

This module provides a dialog for viewing file differences and changes
between project versions and backups.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from pathlib import Path
from typing import Optional

from logic.diff_engine import DirectoryDiff, FileDiff, ChangeType
from logic.backup_manager import BackupInfo


class DiffViewerDialog:
    """
    Dialog for viewing file differences.
    
    Displays directory and file differences in a user-friendly format
    with syntax highlighting and navigation.
    """
    
    def __init__(self, parent, project_name: str, backup_info: BackupInfo, diff: DirectoryDiff):
        """
        Initialize diff viewer dialog.
        
        Args:
            parent: Parent widget
            project_name: Name of the project
            backup_info: Backup being compared
            diff: DirectoryDiff object with comparison results
        """
        self.logger = logging.getLogger(__name__)
        self.project_name = project_name
        self.backup_info = backup_info
        self.diff = diff
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"Compare {project_name} with Backup")
        self.dialog.geometry("1000x700")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Setup UI
        self._setup_ui()
        self._populate_data()
        
        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        # Configure grid
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(1, weight=1)
        
        # Create header
        self._create_header()
        
        # Create main content
        self._create_main_content()
        
        # Create buttons
        self._create_buttons()
    
    def _create_header(self) -> None:
        """Create header with comparison info."""
        header_frame = ttk.Frame(self.dialog)
        header_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=10)
        header_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(
            header_frame, 
            text=f"Comparing: {self.project_name}",
            font=("TkDefaultFont", 12, "bold")
        )
        title_label.grid(row=0, column=0, columnspan=2, sticky="w", pady=(0, 5))
        
        # Backup info
        ttk.Label(header_frame, text="Backup:").grid(row=1, column=0, sticky="w")
        backup_text = f"{self.backup_info.backup_id} ({self.backup_info.timestamp.strftime('%Y-%m-%d %H:%M:%S')})"
        ttk.Label(header_frame, text=backup_text).grid(row=1, column=1, sticky="w", padx=(5, 0))
        
        # Summary
        ttk.Label(header_frame, text="Changes:").grid(row=2, column=0, sticky="w")
        summary_text = (f"{len(self.diff.added_files)} added, "
                       f"{len(self.diff.deleted_files)} deleted, "
                       f"{len(self.diff.modified_files)} modified, "
                       f"{len(self.diff.renamed_files)} renamed")
        ttk.Label(header_frame, text=summary_text).grid(row=2, column=1, sticky="w", padx=(5, 0))
    
    def _create_main_content(self) -> None:
        """Create main content area with file list and diff view."""
        # Create paned window
        paned = ttk.PanedWindow(self.dialog, orient=tk.HORIZONTAL)
        paned.grid(row=1, column=0, sticky="nsew", padx=10, pady=(0, 10))
        
        # Left panel - file list
        self._create_file_list(paned)
        
        # Right panel - diff content
        self._create_diff_content(paned)
        
        # Add panels to paned window
        paned.add(self.file_frame, weight=1)
        paned.add(self.diff_frame, weight=2)
    
    def _create_file_list(self, parent) -> None:
        """Create file list panel."""
        self.file_frame = ttk.LabelFrame(parent, text="Changed Files")
        self.file_frame.columnconfigure(0, weight=1)
        self.file_frame.rowconfigure(0, weight=1)
        
        # Create treeview for files
        columns = ("file", "change", "details")
        self.file_tree = ttk.Treeview(self.file_frame, columns=columns, show="headings")
        
        # Configure columns
        self.file_tree.heading("file", text="File")
        self.file_tree.heading("change", text="Change")
        self.file_tree.heading("details", text="Details")
        
        self.file_tree.column("file", width=200)
        self.file_tree.column("change", width=80)
        self.file_tree.column("details", width=100)
        
        # Add scrollbar
        file_scrollbar = ttk.Scrollbar(self.file_frame, orient=tk.VERTICAL, command=self.file_tree.yview)
        self.file_tree.configure(yscrollcommand=file_scrollbar.set)
        
        # Grid layout
        self.file_tree.grid(row=0, column=0, sticky="nsew")
        file_scrollbar.grid(row=0, column=1, sticky="ns")
        
        # Bind selection event
        self.file_tree.bind("<<TreeviewSelect>>", self.on_file_select)
    
    def _create_diff_content(self, parent) -> None:
        """Create diff content panel."""
        self.diff_frame = ttk.LabelFrame(parent, text="File Content")
        self.diff_frame.columnconfigure(0, weight=1)
        self.diff_frame.rowconfigure(0, weight=1)
        
        # Create text widget for diff content
        self.diff_text = tk.Text(
            self.diff_frame,
            wrap=tk.NONE,
            font=("Courier", 10),
            state=tk.DISABLED
        )
        
        # Add scrollbars
        diff_v_scrollbar = ttk.Scrollbar(self.diff_frame, orient=tk.VERTICAL, command=self.diff_text.yview)
        diff_h_scrollbar = ttk.Scrollbar(self.diff_frame, orient=tk.HORIZONTAL, command=self.diff_text.xview)
        self.diff_text.configure(yscrollcommand=diff_v_scrollbar.set, xscrollcommand=diff_h_scrollbar.set)
        
        # Grid layout
        self.diff_text.grid(row=0, column=0, sticky="nsew")
        diff_v_scrollbar.grid(row=0, column=1, sticky="ns")
        diff_h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Configure text tags for syntax highlighting
        self._configure_diff_tags()
    
    def _configure_diff_tags(self) -> None:
        """Configure text tags for diff highlighting."""
        self.diff_text.tag_configure("added", background="#d4edda", foreground="#155724")
        self.diff_text.tag_configure("deleted", background="#f8d7da", foreground="#721c24")
        self.diff_text.tag_configure("modified", background="#fff3cd", foreground="#856404")
        self.diff_text.tag_configure("context", foreground="#6c757d")
        self.diff_text.tag_configure("header", font=("Courier", 10, "bold"), foreground="#007bff")
    
    def _create_buttons(self) -> None:
        """Create dialog buttons."""
        button_frame = ttk.Frame(self.dialog)
        button_frame.grid(row=2, column=0, sticky="ew", padx=10, pady=(0, 10))
        
        # Export diff button
        ttk.Button(
            button_frame,
            text="Export Diff",
            command=self.export_diff
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # Close button
        ttk.Button(
            button_frame,
            text="Close",
            command=self.dialog.destroy
        ).pack(side=tk.RIGHT)
    
    def _populate_data(self) -> None:
        """Populate file list with diff data."""
        # Add added files
        for file_path in self.diff.added_files:
            self.file_tree.insert("", tk.END, values=(
                file_path,
                "Added",
                "New file"
            ), tags=("added",))
        
        # Add deleted files
        for file_path in self.diff.deleted_files:
            self.file_tree.insert("", tk.END, values=(
                file_path,
                "Deleted",
                "Removed"
            ), tags=("deleted",))
        
        # Add modified files
        for file_diff in self.diff.modified_files:
            if file_diff.binary_file:
                details = "Binary file"
            else:
                details = f"+{file_diff.lines_added} -{file_diff.lines_deleted}"
            
            self.file_tree.insert("", tk.END, values=(
                file_diff.file_path,
                "Modified",
                details
            ), tags=("modified",))
        
        # Add renamed files
        for old_path, new_path in self.diff.renamed_files:
            self.file_tree.insert("", tk.END, values=(
                f"{old_path} → {new_path}",
                "Renamed",
                "Moved"
            ), tags=("renamed",))
        
        # Configure row colors
        self.file_tree.tag_configure("added", background="#d4edda")
        self.file_tree.tag_configure("deleted", background="#f8d7da")
        self.file_tree.tag_configure("modified", background="#fff3cd")
        self.file_tree.tag_configure("renamed", background="#e2e3e5")
    
    def on_file_select(self, event) -> None:
        """Handle file selection in tree."""
        selection = self.file_tree.selection()
        if not selection:
            self._clear_diff_content()
            return
        
        item = selection[0]
        values = self.file_tree.item(item, "values")
        file_path = values[0]
        change_type = values[1]
        
        # Find the corresponding diff data
        if change_type == "Modified":
            # Find the FileDiff object
            for file_diff in self.diff.modified_files:
                if file_diff.file_path == file_path:
                    self._show_file_diff(file_diff)
                    break
        else:
            # For added/deleted/renamed files, show summary
            self._show_file_summary(file_path, change_type)
    
    def _show_file_diff(self, file_diff: FileDiff) -> None:
        """Show diff content for a modified file."""
        self.diff_text.config(state=tk.NORMAL)
        self.diff_text.delete(1.0, tk.END)
        
        if file_diff.binary_file:
            content = f"Binary file: {file_diff.file_path}\n\n"
            content += "Binary files cannot be compared in text mode.\n"
            content += f"File size or content has changed.\n"
            content += f"Similarity: {file_diff.similarity:.1%}"
            
            self.diff_text.insert(tk.END, content)
        else:
            # Show unified diff
            if file_diff.diff_content:
                lines = file_diff.diff_content.split('\n')
                
                for line in lines:
                    if line.startswith('+++') or line.startswith('---') or line.startswith('@@'):
                        self.diff_text.insert(tk.END, line + '\n', "header")
                    elif line.startswith('+'):
                        self.diff_text.insert(tk.END, line + '\n', "added")
                    elif line.startswith('-'):
                        self.diff_text.insert(tk.END, line + '\n', "deleted")
                    else:
                        self.diff_text.insert(tk.END, line + '\n', "context")
            else:
                self.diff_text.insert(tk.END, f"No diff content available for {file_diff.file_path}")
        
        self.diff_text.config(state=tk.DISABLED)
    
    def _show_file_summary(self, file_path: str, change_type: str) -> None:
        """Show summary for added/deleted/renamed files."""
        self.diff_text.config(state=tk.NORMAL)
        self.diff_text.delete(1.0, tk.END)
        
        content = f"File: {file_path}\n"
        content += f"Change Type: {change_type}\n\n"
        
        if change_type == "Added":
            content += "This file was added in the current version.\n"
            content += "It does not exist in the backup."
        elif change_type == "Deleted":
            content += "This file was deleted from the current version.\n"
            content += "It exists in the backup but not in the current project."
        elif change_type == "Renamed":
            content += "This file was renamed or moved.\n"
            content += "The content may be the same but the location changed."
        
        self.diff_text.insert(tk.END, content)
        self.diff_text.config(state=tk.DISABLED)
    
    def _clear_diff_content(self) -> None:
        """Clear diff content area."""
        self.diff_text.config(state=tk.NORMAL)
        self.diff_text.delete(1.0, tk.END)
        self.diff_text.insert(tk.END, "Select a file to view differences")
        self.diff_text.config(state=tk.DISABLED)
    
    def export_diff(self) -> None:
        """Export diff to file."""
        from tkinter import filedialog
        
        file_path = filedialog.asksaveasfilename(
            title="Export Diff",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    # Write header
                    f.write(f"Diff Report: {self.project_name}\n")
                    f.write(f"Backup: {self.backup_info.backup_id}\n")
                    f.write(f"Generated: {tk.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 60 + "\n\n")
                    
                    # Write summary
                    f.write("Summary:\n")
                    f.write(f"  Added files: {len(self.diff.added_files)}\n")
                    f.write(f"  Deleted files: {len(self.diff.deleted_files)}\n")
                    f.write(f"  Modified files: {len(self.diff.modified_files)}\n")
                    f.write(f"  Renamed files: {len(self.diff.renamed_files)}\n\n")
                    
                    # Write detailed changes
                    if self.diff.added_files:
                        f.write("Added Files:\n")
                        for file_path in self.diff.added_files:
                            f.write(f"  + {file_path}\n")
                        f.write("\n")
                    
                    if self.diff.deleted_files:
                        f.write("Deleted Files:\n")
                        for file_path in self.diff.deleted_files:
                            f.write(f"  - {file_path}\n")
                        f.write("\n")
                    
                    if self.diff.renamed_files:
                        f.write("Renamed Files:\n")
                        for old_path, new_path in self.diff.renamed_files:
                            f.write(f"  {old_path} → {new_path}\n")
                        f.write("\n")
                    
                    if self.diff.modified_files:
                        f.write("Modified Files:\n")
                        for file_diff in self.diff.modified_files:
                            f.write(f"  M {file_diff.file_path}")
                            if not file_diff.binary_file:
                                f.write(f" (+{file_diff.lines_added} -{file_diff.lines_deleted})")
                            f.write("\n")
                        f.write("\n")
                    
                    # Write detailed diffs for modified files
                    for file_diff in self.diff.modified_files:
                        if file_diff.diff_content and not file_diff.binary_file:
                            f.write(f"Diff for {file_diff.file_path}:\n")
                            f.write("-" * 40 + "\n")
                            f.write(file_diff.diff_content)
                            f.write("\n" + "=" * 40 + "\n\n")
                
                messagebox.showinfo("Export Complete", f"Diff exported to:\n{file_path}")
            
            except Exception as e:
                messagebox.showerror("Export Failed", f"Failed to export diff:\n{e}")
