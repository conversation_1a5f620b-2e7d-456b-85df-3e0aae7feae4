"""
Simatic project scanner for SIMAVER application.

This module provides functionality to detect, scan, and analyze Simatic Manager
PLC projects on the file system.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Optional, Set
from dataclasses import dataclass
from datetime import datetime
import json


@dataclass
class SimaticProject:
    """
    Represents a Simatic Manager project.
    
    Contains metadata and information about a detected Simatic project.
    """
    name: str
    path: Path
    project_file: Path
    project_type: str  # 'STEP7', 'TIA_Portal', 'STEP5', etc.
    version: Optional[str] = None
    last_modified: Optional[datetime] = None
    size_bytes: int = 0
    description: Optional[str] = None
    
    def to_dict(self) -> Dict:
        """Convert project to dictionary for serialization."""
        return {
            'name': self.name,
            'path': str(self.path),
            'project_file': str(self.project_file),
            'project_type': self.project_type,
            'version': self.version,
            'last_modified': self.last_modified.isoformat() if self.last_modified else None,
            'size_bytes': self.size_bytes,
            'description': self.description
        }


class ProjectScanner:
    """
    Scans file system for Simatic Manager projects.
    
    Detects various types of Simatic projects including STEP 7, TIA Portal,
    and legacy STEP 5 projects.
    """
    
    # File patterns for different project types
    PROJECT_PATTERNS = {
        'STEP7': {
            'extensions': ['.s7p'],
            'required_files': ['*.s7p'],
            'optional_files': ['*.s7l', '*.awl', '*.scl', '*.fbd', '*.lad']
        },
        'TIA_Portal': {
            'extensions': ['.ap15', '.ap16', '.ap17', '.ap18'],
            'required_files': ['*.ap*'],
            'optional_files': ['*.scl', '*.awl', '*.fbd', '*.lad']
        },
        'STEP5': {
            'extensions': ['.s5d'],
            'required_files': ['*.s5d'],
            'optional_files': ['*.awl', '*.s5']
        },
        'WinCC': {
            'extensions': ['.mcp'],
            'required_files': ['*.mcp'],
            'optional_files': ['*.pdl', '*.fct']
        }
    }
    
    def __init__(self):
        """Initialize project scanner."""
        self.logger = logging.getLogger(__name__)
        self.found_projects: List[SimaticProject] = []
    
    def scan_directory(self, directory: Path, recursive: bool = True) -> List[SimaticProject]:
        """
        Scan directory for Simatic projects.
        
        Args:
            directory: Directory to scan
            recursive: Whether to scan subdirectories
            
        Returns:
            List of found Simatic projects
        """
        self.logger.info(f"Scanning directory: {directory}")
        projects = []
        
        if not directory.exists() or not directory.is_dir():
            self.logger.warning(f"Directory does not exist or is not a directory: {directory}")
            return projects
        
        try:
            # Get all files in directory
            if recursive:
                files = list(directory.rglob('*'))
            else:
                files = list(directory.iterdir())
            
            # Group files by directory
            dir_files = {}
            for file_path in files:
                if file_path.is_file():
                    parent_dir = file_path.parent
                    if parent_dir not in dir_files:
                        dir_files[parent_dir] = []
                    dir_files[parent_dir].append(file_path)
            
            # Check each directory for project patterns
            for dir_path, file_list in dir_files.items():
                project = self._detect_project_in_directory(dir_path, file_list)
                if project:
                    projects.append(project)
                    self.logger.info(f"Found {project.project_type} project: {project.name}")
        
        except Exception as e:
            self.logger.error(f"Error scanning directory {directory}: {e}")
        
        self.found_projects = projects
        return projects
    
    def _detect_project_in_directory(self, directory: Path, files: List[Path]) -> Optional[SimaticProject]:
        """
        Detect if directory contains a Simatic project.
        
        Args:
            directory: Directory to check
            files: List of files in directory
            
        Returns:
            SimaticProject if detected, None otherwise
        """
        file_names = [f.name.lower() for f in files]
        file_extensions = set(f.suffix.lower() for f in files)
        
        # Check each project type
        for project_type, patterns in self.PROJECT_PATTERNS.items():
            # Check if any required extensions are present
            required_extensions = set(patterns['extensions'])
            if required_extensions.intersection(file_extensions):
                # Find the main project file
                main_file = self._find_main_project_file(files, patterns['extensions'])
                if main_file:
                    return self._create_project_info(directory, main_file, project_type, files)
        
        return None
    
    def _find_main_project_file(self, files: List[Path], extensions: List[str]) -> Optional[Path]:
        """
        Find the main project file from a list of files.
        
        Args:
            files: List of files to search
            extensions: Valid extensions for main project file
            
        Returns:
            Path to main project file or None
        """
        for file_path in files:
            if file_path.suffix.lower() in extensions:
                return file_path
        return None
    
    def _create_project_info(self, directory: Path, main_file: Path, 
                           project_type: str, all_files: List[Path]) -> SimaticProject:
        """
        Create SimaticProject instance with metadata.
        
        Args:
            directory: Project directory
            main_file: Main project file
            project_type: Type of project
            all_files: All files in project directory
            
        Returns:
            SimaticProject instance
        """
        # Calculate project size
        total_size = 0
        for file_path in all_files:
            try:
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            except (OSError, IOError):
                pass
        
        # Get last modified time
        try:
            last_modified = datetime.fromtimestamp(main_file.stat().st_mtime)
        except (OSError, IOError):
            last_modified = None
        
        # Extract project name (without extension)
        project_name = main_file.stem
        
        # Try to extract version info (if available)
        version = self._extract_version_info(main_file)
        
        return SimaticProject(
            name=project_name,
            path=directory,
            project_file=main_file,
            project_type=project_type,
            version=version,
            last_modified=last_modified,
            size_bytes=total_size,
            description=f"{project_type} project in {directory.name}"
        )
    
    def _extract_version_info(self, project_file: Path) -> Optional[str]:
        """
        Extract version information from project file.
        
        Args:
            project_file: Path to project file
            
        Returns:
            Version string if found, None otherwise
        """
        # This is a simplified version extraction
        # In a real implementation, you would parse the actual project file format
        try:
            # For now, just return the file modification date as version
            stat = project_file.stat()
            return f"Modified: {datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M')}"
        except Exception:
            return None
    
    def get_project_files(self, project: SimaticProject) -> List[Path]:
        """
        Get all files belonging to a project.
        
        Args:
            project: SimaticProject instance
            
        Returns:
            List of file paths belonging to the project
        """
        project_files = []
        
        try:
            # Get all files in project directory
            for file_path in project.path.rglob('*'):
                if file_path.is_file():
                    # Check if file is relevant to the project type
                    if self._is_project_related_file(file_path, project.project_type):
                        project_files.append(file_path)
        
        except Exception as e:
            self.logger.error(f"Error getting project files for {project.name}: {e}")
        
        return project_files
    
    def _is_project_related_file(self, file_path: Path, project_type: str) -> bool:
        """
        Check if file is related to the project type.
        
        Args:
            file_path: File to check
            project_type: Type of project
            
        Returns:
            True if file is project-related
        """
        file_ext = file_path.suffix.lower()
        
        # Get patterns for project type
        patterns = self.PROJECT_PATTERNS.get(project_type, {})
        all_extensions = patterns.get('extensions', [])
        
        # Add optional file extensions
        if 'optional_files' in patterns:
            for pattern in patterns['optional_files']:
                if pattern.startswith('*.'):
                    all_extensions.append(pattern[1:])  # Remove '*'
        
        return file_ext in all_extensions
    
    def export_project_list(self, output_file: Path) -> None:
        """
        Export found projects to JSON file.
        
        Args:
            output_file: Path to output JSON file
        """
        try:
            projects_data = [project.to_dict() for project in self.found_projects]
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(projects_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Project list exported to {output_file}")
        except Exception as e:
            self.logger.error(f"Error exporting project list: {e}")
