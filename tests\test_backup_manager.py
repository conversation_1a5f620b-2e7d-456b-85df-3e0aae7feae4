"""
Unit tests for backup manager functionality.

Tests backup creation, restoration, management, and metadata handling.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import logging

# Disable logging during tests
logging.disable(logging.CRITICAL)

from logic.backup_manager import BackupManager, BackupInfo
from logic.project_scanner import SimaticProject


class TestBackupManager(unittest.TestCase):
    """Test cases for BackupManager class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for tests
        self.test_dir = Path(tempfile.mkdtemp())
        self.backup_dir = self.test_dir / 'backups'
        self.projects_dir = self.test_dir / 'projects'
        
        # Create directories
        self.backup_dir.mkdir(parents=True)
        self.projects_dir.mkdir(parents=True)
        
        # Initialize backup manager
        self.backup_manager = BackupManager(self.backup_dir)
        
        # Create test project
        self.test_project = self._create_test_project()
    
    def tearDown(self):
        """Clean up test environment."""
        # Close backup manager
        self.backup_manager.close()
        
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Re-enable logging
        logging.disable(logging.NOTSET)
    
    def _create_test_project(self) -> SimaticProject:
        """Create a test Simatic project with files."""
        project_dir = self.projects_dir / 'TestProject'
        project_dir.mkdir()
        
        # Create main project file
        project_file = project_dir / 'TestProject.s7p'
        project_file.write_text('Main project file content\nLine 2\nLine 3')
        
        # Create subdirectory with files
        sub_dir = project_dir / 'Programs'
        sub_dir.mkdir()
        
        (sub_dir / 'Main.awl').write_text('AWL program content')
        (sub_dir / 'Function1.scl').write_text('SCL function content')
        (project_dir / 'Config.ini').write_text('[Settings]\nValue=123')
        
        return SimaticProject(
            name='TestProject',
            path=project_dir,
            project_file=project_file,
            project_type='STEP7',
            last_modified=datetime.now()
        )
    
    def test_create_compressed_backup(self):
        """Test creating a compressed backup."""
        # Create backup
        backup_info = self.backup_manager.create_backup(
            self.test_project,
            description='Test compressed backup',
            compress=True
        )
        
        # Verify backup info
        self.assertIsNotNone(backup_info)
        self.assertEqual(backup_info.project_name, 'TestProject')
        self.assertEqual(backup_info.description, 'Test compressed backup')
        self.assertGreater(backup_info.file_count, 0)
        self.assertGreater(backup_info.size_bytes, 0)
        
        # Verify backup file exists
        backup_path = Path(backup_info.backup_path)
        self.assertTrue(backup_path.exists())
        self.assertEqual(backup_path.suffix, '.zip')
    
    def test_create_directory_backup(self):
        """Test creating a directory backup."""
        # Create backup
        backup_info = self.backup_manager.create_backup(
            self.test_project,
            description='Test directory backup',
            compress=False
        )
        
        # Verify backup info
        self.assertIsNotNone(backup_info)
        self.assertEqual(backup_info.project_name, 'TestProject')
        self.assertEqual(backup_info.description, 'Test directory backup')
        
        # Verify backup directory exists
        backup_path = Path(backup_info.backup_path)
        self.assertTrue(backup_path.exists())
        self.assertTrue(backup_path.is_dir())
        
        # Verify files were copied
        self.assertTrue((backup_path / 'TestProject.s7p').exists())
        self.assertTrue((backup_path / 'Programs' / 'Main.awl').exists())
    
    def test_list_backups(self):
        """Test listing backups."""
        # Create multiple backups
        backup1 = self.backup_manager.create_backup(
            self.test_project, 'First backup'
        )
        backup2 = self.backup_manager.create_backup(
            self.test_project, 'Second backup'
        )
        
        # List all backups
        all_backups = self.backup_manager.list_backups()
        self.assertEqual(len(all_backups), 2)
        
        # List backups for specific project
        project_backups = self.backup_manager.list_backups('TestProject')
        self.assertEqual(len(project_backups), 2)
        
        # Verify backups are sorted by timestamp (newest first)
        self.assertGreater(
            project_backups[0].timestamp,
            project_backups[1].timestamp
        )
    
    def test_restore_compressed_backup(self):
        """Test restoring a compressed backup."""
        # Create backup
        backup_info = self.backup_manager.create_backup(
            self.test_project, 'Test backup', compress=True
        )
        
        # Create restore location
        restore_dir = self.test_dir / 'restored'
        
        # Restore backup
        success = self.backup_manager.restore_backup(backup_info, restore_dir)
        
        # Verify restoration
        self.assertTrue(success)
        self.assertTrue(restore_dir.exists())
        self.assertTrue((restore_dir / 'TestProject.s7p').exists())
        self.assertTrue((restore_dir / 'Programs' / 'Main.awl').exists())
        
        # Verify content
        restored_content = (restore_dir / 'TestProject.s7p').read_text()
        original_content = self.test_project.project_file.read_text()
        self.assertEqual(restored_content, original_content)
    
    def test_restore_directory_backup(self):
        """Test restoring a directory backup."""
        # Create backup
        backup_info = self.backup_manager.create_backup(
            self.test_project, 'Test backup', compress=False
        )
        
        # Create restore location
        restore_dir = self.test_dir / 'restored'
        
        # Restore backup
        success = self.backup_manager.restore_backup(backup_info, restore_dir)
        
        # Verify restoration
        self.assertTrue(success)
        self.assertTrue(restore_dir.exists())
        self.assertTrue((restore_dir / 'TestProject.s7p').exists())
    
    def test_delete_backup(self):
        """Test deleting a backup."""
        # Create backup
        backup_info = self.backup_manager.create_backup(
            self.test_project, 'Test backup'
        )
        
        # Verify backup exists
        backup_path = Path(backup_info.backup_path)
        self.assertTrue(backup_path.exists())
        
        # Delete backup
        success = self.backup_manager.delete_backup(backup_info)
        
        # Verify deletion
        self.assertTrue(success)
        self.assertFalse(backup_path.exists())
        
        # Verify backup is removed from database
        backups = self.backup_manager.list_backups('TestProject')
        self.assertEqual(len(backups), 0)
    
    def test_cleanup_old_backups(self):
        """Test cleaning up old backups."""
        # Create multiple backups
        backups = []
        for i in range(5):
            backup = self.backup_manager.create_backup(
                self.test_project, f'Backup {i}'
            )
            backups.append(backup)
        
        # Cleanup old backups (keep only 3)
        deleted_count = self.backup_manager.cleanup_old_backups('TestProject', 3)
        
        # Verify cleanup
        self.assertEqual(deleted_count, 2)
        
        # Verify remaining backups
        remaining_backups = self.backup_manager.list_backups('TestProject')
        self.assertEqual(len(remaining_backups), 3)
        
        # Verify newest backups were kept
        remaining_ids = {b.backup_id for b in remaining_backups}
        newest_ids = {b.backup_id for b in backups[-3:]}  # Last 3 backups
        self.assertEqual(remaining_ids, newest_ids)
    
    def test_backup_statistics(self):
        """Test getting backup statistics."""
        # Create backups for different projects
        backup1 = self.backup_manager.create_backup(
            self.test_project, 'Backup 1'
        )
        
        # Create second project
        project2 = self._create_test_project()
        project2.name = 'TestProject2'
        project2.path = self.projects_dir / 'TestProject2'
        project2.path.mkdir()
        (project2.path / 'TestProject2.s7p').write_text('Content 2')
        
        backup2 = self.backup_manager.create_backup(project2, 'Backup 2')
        
        # Get statistics
        stats = self.backup_manager.get_backup_statistics()
        
        # Verify statistics
        self.assertEqual(stats['total_backups'], 2)
        self.assertEqual(stats['unique_projects'], 2)
        self.assertGreater(stats['total_size_bytes'], 0)
        self.assertGreater(stats['average_size_bytes'], 0)
    
    def test_backup_with_tags(self):
        """Test creating backup with tags."""
        # Create backup with tags
        tags = ['manual', 'important', 'release']
        backup_info = self.backup_manager.create_backup(
            self.test_project,
            description='Tagged backup',
            tags=tags
        )
        
        # Verify tags
        self.assertEqual(backup_info.tags, tags)
        
        # Verify tags are persisted
        backups = self.backup_manager.list_backups('TestProject')
        self.assertEqual(backups[0].tags, tags)
    
    def test_backup_checksum_verification(self):
        """Test backup checksum verification."""
        # Create backup
        backup_info = self.backup_manager.create_backup(
            self.test_project, 'Checksum test'
        )
        
        # Verify checksum is generated
        self.assertIsNotNone(backup_info.checksum)
        self.assertGreater(len(backup_info.checksum), 0)
        
        # Create another backup of the same project
        backup_info2 = self.backup_manager.create_backup(
            self.test_project, 'Checksum test 2'
        )
        
        # Checksums should be the same for identical content
        self.assertEqual(backup_info.checksum, backup_info2.checksum)


if __name__ == '__main__':
    unittest.main()
