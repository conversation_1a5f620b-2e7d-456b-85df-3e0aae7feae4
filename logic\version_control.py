"""
Core version control functionality for SIMAVER application.

This module provides the main version control operations, coordinating
between backup management, file monitoring, and project scanning.
"""

import logging
import threading
from pathlib import Path
from typing import List, Dict, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

from .project_scanner import ProjectScanner, SimaticProject
from .backup_manager import BackupManager, BackupInfo
from .file_monitor import FileMonitor, FileChangeEvent
from .diff_engine import Diff<PERSON><PERSON><PERSON>, DirectoryDiff
from config.settings import settings


@dataclass
class ProjectStatus:
    """
    Current status of a monitored project.
    
    Contains information about project state and recent activity.
    """
    project: SimaticProject
    last_backup: Optional[BackupInfo]
    has_changes: bool
    change_count: int
    last_change_time: Optional[datetime]
    monitoring_active: bool
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'project': self.project.to_dict(),
            'last_backup': self.last_backup.to_dict() if self.last_backup else None,
            'has_changes': self.has_changes,
            'change_count': self.change_count,
            'last_change_time': self.last_change_time.isoformat() if self.last_change_time else None,
            'monitoring_active': self.monitoring_active
        }


class VersionControl:
    """
    Main version control system for Simatic projects.
    
    Coordinates all version control operations including project scanning,
    backup management, file monitoring, and change detection.
    """
    
    def __init__(self):
        """Initialize version control system."""
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.project_scanner = ProjectScanner()
        self.backup_manager = BackupManager(settings.backup_dir)
        self.file_monitor = FileMonitor()
        self.diff_engine = DiffEngine()
        
        # State management
        self.monitored_projects: Dict[str, SimaticProject] = {}
        self.project_changes: Dict[str, List[FileChangeEvent]] = {}
        self.auto_backup_enabled = settings.get('general', 'auto_backup', True)
        self.backup_interval = settings.get('general', 'backup_interval', 30)  # minutes
        
        # Threading
        self._lock = threading.Lock()
        self.auto_backup_timer: Optional[threading.Timer] = None
        
        # Event callbacks
        self.change_callbacks: List[Callable[[str, FileChangeEvent], None]] = []
        self.backup_callbacks: List[Callable[[str, BackupInfo], None]] = []
        
        # Setup file monitor callback
        self.file_monitor.add_change_callback(self._on_file_change)
        
        self.logger.info("Version control system initialized")
    
    def scan_for_projects(self, directory: Path, recursive: bool = True) -> List[SimaticProject]:
        """
        Scan directory for Simatic projects.
        
        Args:
            directory: Directory to scan
            recursive: Whether to scan subdirectories
            
        Returns:
            List of found projects
        """
        self.logger.info(f"Scanning for projects in: {directory}")
        
        projects = self.project_scanner.scan_directory(directory, recursive)
        
        self.logger.info(f"Found {len(projects)} projects")
        return projects
    
    def add_project(self, project: SimaticProject, start_monitoring: bool = True) -> None:
        """
        Add project to version control.
        
        Args:
            project: SimaticProject to add
            start_monitoring: Whether to start monitoring the project
        """
        self.logger.info(f"Adding project to version control: {project.name}")
        
        with self._lock:
            self.monitored_projects[project.name] = project
            self.project_changes[project.name] = []
        
        if start_monitoring:
            self.file_monitor.add_project(project)
        
        # Create initial backup if auto-backup is enabled
        if self.auto_backup_enabled:
            self.create_backup(project.name, "Initial backup")
        
        self.logger.info(f"Project added: {project.name}")
    
    def remove_project(self, project_name: str) -> None:
        """
        Remove project from version control.
        
        Args:
            project_name: Name of project to remove
        """
        self.logger.info(f"Removing project from version control: {project_name}")
        
        with self._lock:
            if project_name in self.monitored_projects:
                del self.monitored_projects[project_name]
            
            if project_name in self.project_changes:
                del self.project_changes[project_name]
        
        self.file_monitor.remove_project(project_name)
        
        self.logger.info(f"Project removed: {project_name}")
    
    def create_backup(self, project_name: str, description: Optional[str] = None,
                     tags: Optional[List[str]] = None) -> Optional[BackupInfo]:
        """
        Create backup for a project.
        
        Args:
            project_name: Name of project to backup
            description: Optional backup description
            tags: Optional backup tags
            
        Returns:
            BackupInfo if successful, None otherwise
        """
        if project_name not in self.monitored_projects:
            self.logger.error(f"Project not found: {project_name}")
            return None
        
        project = self.monitored_projects[project_name]
        
        try:
            backup_info = self.backup_manager.create_backup(
                project, description, tags
            )
            
            # Clear change tracking for this project
            with self._lock:
                self.project_changes[project_name] = []
            
            # Notify callbacks
            for callback in self.backup_callbacks:
                try:
                    callback(project_name, backup_info)
                except Exception as e:
                    self.logger.error(f"Error in backup callback: {e}")
            
            # Cleanup old backups if configured
            max_backups = settings.get('general', 'max_backups', 50)
            if max_backups > 0:
                self.backup_manager.cleanup_old_backups(project_name, max_backups)
            
            self.logger.info(f"Backup created for {project_name}: {backup_info.backup_id}")
            return backup_info
        
        except Exception as e:
            self.logger.error(f"Error creating backup for {project_name}: {e}")
            return None
    
    def restore_backup(self, backup_info: BackupInfo, 
                      restore_path: Optional[Path] = None) -> bool:
        """
        Restore a backup.
        
        Args:
            backup_info: Backup to restore
            restore_path: Optional custom restore location
            
        Returns:
            True if restore successful
        """
        self.logger.info(f"Restoring backup: {backup_info.backup_id}")
        
        try:
            success = self.backup_manager.restore_backup(backup_info, restore_path)
            
            if success:
                self.logger.info(f"Backup restored successfully: {backup_info.backup_id}")
                
                # If restoring to original location, clear change tracking
                if restore_path is None or str(restore_path) == backup_info.project_path:
                    project_name = backup_info.project_name
                    if project_name in self.project_changes:
                        with self._lock:
                            self.project_changes[project_name] = []
            
            return success
        
        except Exception as e:
            self.logger.error(f"Error restoring backup: {e}")
            return False
    
    def get_project_status(self, project_name: str) -> Optional[ProjectStatus]:
        """
        Get current status of a project.
        
        Args:
            project_name: Name of project
            
        Returns:
            ProjectStatus if project exists, None otherwise
        """
        if project_name not in self.monitored_projects:
            return None
        
        project = self.monitored_projects[project_name]
        
        # Get latest backup
        backups = self.backup_manager.list_backups(project_name)
        last_backup = backups[0] if backups else None
        
        # Get change information
        changes = self.project_changes.get(project_name, [])
        has_changes = len(changes) > 0
        change_count = len(changes)
        last_change_time = changes[-1].timestamp if changes else None
        
        # Check if monitoring is active
        monitoring_status = self.file_monitor.get_monitoring_status()
        monitoring_active = (monitoring_status['is_monitoring'] and 
                           project_name in monitoring_status['monitored_projects'])
        
        return ProjectStatus(
            project=project,
            last_backup=last_backup,
            has_changes=has_changes,
            change_count=change_count,
            last_change_time=last_change_time,
            monitoring_active=monitoring_active
        )
    
    def get_all_project_statuses(self) -> Dict[str, ProjectStatus]:
        """Get status for all monitored projects."""
        statuses = {}
        
        for project_name in self.monitored_projects:
            status = self.get_project_status(project_name)
            if status:
                statuses[project_name] = status
        
        return statuses
    
    def compare_with_backup(self, project_name: str, 
                          backup_info: BackupInfo) -> Optional[DirectoryDiff]:
        """
        Compare current project state with a backup.
        
        Args:
            project_name: Name of project
            backup_info: Backup to compare with
            
        Returns:
            DirectoryDiff if successful, None otherwise
        """
        if project_name not in self.monitored_projects:
            self.logger.error(f"Project not found: {project_name}")
            return None
        
        project = self.monitored_projects[project_name]
        
        try:
            # For compressed backups, we need to extract to temp directory first
            backup_path = Path(backup_info.backup_path)
            
            if backup_path.suffix == '.zip':
                # Extract to temporary directory
                temp_dir = settings.temp_dir / f"compare_{backup_info.backup_id}"
                temp_dir.mkdir(parents=True, exist_ok=True)
                
                import zipfile
                with zipfile.ZipFile(backup_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                diff = self.diff_engine.compare_directories(temp_dir, project.path)
                
                # Cleanup temp directory
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
            else:
                diff = self.diff_engine.compare_directories(backup_path, project.path)
            
            return diff
        
        except Exception as e:
            self.logger.error(f"Error comparing with backup: {e}")
            return None
    
    def start_monitoring(self) -> None:
        """Start file system monitoring for all projects."""
        self.logger.info("Starting file system monitoring")
        
        try:
            self.file_monitor.start_monitoring()
            
            # Start auto-backup timer if enabled
            if self.auto_backup_enabled:
                self._start_auto_backup_timer()
            
            self.logger.info("File system monitoring started")
        
        except Exception as e:
            self.logger.error(f"Error starting monitoring: {e}")
            raise
    
    def stop_monitoring(self) -> None:
        """Stop file system monitoring."""
        self.logger.info("Stopping file system monitoring")
        
        try:
            self.file_monitor.stop_monitoring()
            
            # Stop auto-backup timer
            if self.auto_backup_timer:
                self.auto_backup_timer.cancel()
                self.auto_backup_timer = None
            
            self.logger.info("File system monitoring stopped")
        
        except Exception as e:
            self.logger.error(f"Error stopping monitoring: {e}")
    
    def _on_file_change(self, event: FileChangeEvent) -> None:
        """Handle file change events from monitor."""
        if event.project_name and event.project_name in self.monitored_projects:
            with self._lock:
                if event.project_name not in self.project_changes:
                    self.project_changes[event.project_name] = []
                
                self.project_changes[event.project_name].append(event)
                
                # Limit the number of stored events
                max_events = 1000
                if len(self.project_changes[event.project_name]) > max_events:
                    self.project_changes[event.project_name] = \
                        self.project_changes[event.project_name][-max_events:]
            
            # Notify callbacks
            for callback in self.change_callbacks:
                try:
                    callback(event.project_name, event)
                except Exception as e:
                    self.logger.error(f"Error in change callback: {e}")
            
            self.logger.debug(f"File change recorded: {event.file_path} in {event.project_name}")
    
    def _start_auto_backup_timer(self) -> None:
        """Start the auto-backup timer."""
        if self.auto_backup_timer:
            self.auto_backup_timer.cancel()
        
        interval_seconds = self.backup_interval * 60  # Convert minutes to seconds
        self.auto_backup_timer = threading.Timer(interval_seconds, self._auto_backup_check)
        self.auto_backup_timer.start()
        
        self.logger.debug(f"Auto-backup timer started ({self.backup_interval} minutes)")
    
    def _auto_backup_check(self) -> None:
        """Check if any projects need auto-backup."""
        try:
            for project_name in self.monitored_projects:
                status = self.get_project_status(project_name)
                
                if status and status.has_changes:
                    # Check if enough time has passed since last backup
                    if status.last_backup:
                        time_since_backup = datetime.now() - status.last_backup.timestamp
                        min_interval = timedelta(minutes=self.backup_interval)
                        
                        if time_since_backup >= min_interval:
                            self.create_backup(project_name, "Automatic backup")
                    else:
                        # No previous backup, create one
                        self.create_backup(project_name, "Automatic backup")
            
            # Restart timer
            if self.auto_backup_enabled:
                self._start_auto_backup_timer()
        
        except Exception as e:
            self.logger.error(f"Error in auto-backup check: {e}")
    
    def add_change_callback(self, callback: Callable[[str, FileChangeEvent], None]) -> None:
        """Add callback for file change events."""
        self.change_callbacks.append(callback)
    
    def add_backup_callback(self, callback: Callable[[str, BackupInfo], None]) -> None:
        """Add callback for backup events."""
        self.backup_callbacks.append(callback)
    
    def get_statistics(self) -> Dict:
        """Get version control statistics."""
        backup_stats = self.backup_manager.get_backup_statistics()
        monitoring_stats = self.file_monitor.get_monitoring_status()
        
        total_changes = sum(len(changes) for changes in self.project_changes.values())
        
        return {
            'monitored_projects': len(self.monitored_projects),
            'total_backups': backup_stats.get('total_backups', 0),
            'total_backup_size': backup_stats.get('total_size_bytes', 0),
            'total_changes_tracked': total_changes,
            'monitoring_active': monitoring_stats['is_monitoring'],
            'auto_backup_enabled': self.auto_backup_enabled
        }
    
    def shutdown(self) -> None:
        """Shutdown version control system."""
        self.logger.info("Shutting down version control system")
        
        try:
            self.stop_monitoring()
            self.backup_manager.close()
            
            self.logger.info("Version control system shutdown complete")
        
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
