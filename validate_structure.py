#!/usr/bin/env python3
"""
Structure validation script for SIMAVER.

This script validates that all required files and modules are present
and can be imported correctly.
"""

import sys
import os
from pathlib import Path

def validate_structure():
    """Validate project structure and imports."""
    print("SIMAVER Structure Validation")
    print("=" * 40)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    else:
        print("✅ Python version OK")
    
    # Check project structure
    required_files = [
        'main.py',
        'requirements.txt',
        'install.bat',
        'README.md',
        'config/__init__.py',
        'config/settings.py',
        'logic/__init__.py',
        'logic/version_control.py',
        'logic/backup_manager.py',
        'logic/file_monitor.py',
        'logic/diff_engine.py',
        'logic/project_scanner.py',
        'ui/__init__.py',
        'ui/main_window.py',
        'ui/project_manager.py',
        'ui/backup_scheduler.py',
        'ui/diff_viewer.py',
        'ui/settings_dialog.py',
        'tests/__init__.py',
        'tests/test_version_control.py',
        'tests/test_backup_manager.py',
        'tests/test_diff_engine.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All required files present")
    
    # Check directories
    required_dirs = [
        'config',
        'logic',
        'ui',
        'tests',
        'assets',
        'assets/icons',
        'assets/styles'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print("❌ Missing directories:")
        for dir_path in missing_dirs:
            print(f"   - {dir_path}")
        return False
    else:
        print("✅ All required directories present")
    
    # Test basic imports (without external dependencies)
    print("\nTesting imports...")
    
    try:
        # Test config module
        sys.path.insert(0, '.')
        from config.settings import Settings
        print("✅ Config module imports OK")
    except Exception as e:
        print(f"❌ Config module import failed: {e}")
        return False
    
    try:
        # Test logic modules (basic structure)
        from logic.project_scanner import SimaticProject, ProjectScanner
        from logic.diff_engine import DiffEngine, ChangeType
        print("✅ Logic modules import OK")
    except Exception as e:
        print(f"❌ Logic modules import failed: {e}")
        return False
    
    # Check requirements.txt
    try:
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
            if 'watchdog' in requirements and 'tkinter' in requirements:
                print("✅ Requirements file looks good")
            else:
                print("⚠️  Requirements file may be incomplete")
    except Exception as e:
        print(f"❌ Error reading requirements: {e}")
        return False
    
    print("\n" + "=" * 40)
    print("✅ Structure validation PASSED")
    print("\nNext steps:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Run tests: python -m pytest tests/")
    print("3. Start application: python main.py")
    
    return True

if __name__ == "__main__":
    success = validate_structure()
    sys.exit(0 if success else 1)
