#!/usr/bin/env python3
"""
SIMAVER Launcher Script

This script provides a safe way to launch SIMAVER with dependency checking
and helpful error messages.
"""

import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or later is required")
        print(f"Current version: {sys.version}")
        print("Please upgrade Python and try again.")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_tkinter():
    """Check if tkinter is available."""
    try:
        import tkinter
        print("✅ tkinter available")
        return True
    except ImportError:
        print("❌ tkinter not available")
        print("Please install tkinter:")
        print("  Ubuntu/Debian: sudo apt-get install python3-tk")
        print("  CentOS/RHEL: sudo yum install tkinter")
        print("  Windows: tkinter should be included with Python")
        return False

def check_critical_dependencies():
    """Check critical dependencies and offer installation."""
    critical_deps = ['watchdog', 'psutil', 'PIL']
    missing = []
    
    for dep in critical_deps:
        try:
            __import__(dep)
            print(f"✅ {dep} available")
        except ImportError:
            missing.append(dep)
            print(f"❌ {dep} not available")
    
    if missing:
        print(f"\nMissing critical dependencies: {', '.join(missing)}")
        print("\nTo install missing dependencies, run one of:")
        print("  pip install watchdog psutil pillow")
        print("  pip install -r requirements-minimal.txt")
        print("  pip install -r requirements.txt")
        
        response = input("\nTry to install automatically? (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            return install_dependencies()
        else:
            return False
    
    return True

def install_dependencies():
    """Try to install dependencies automatically."""
    print("\nAttempting to install dependencies...")
    
    # Try minimal installation first
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 
            'watchdog', 'psutil', 'pillow'
        ])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Automatic installation failed")
        print("Please install manually:")
        print("  pip install watchdog psutil pillow")
        return False

def run_basic_test():
    """Run basic functionality test."""
    print("\nRunning basic functionality test...")
    try:
        result = subprocess.run([sys.executable, 'test_basic.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Basic test passed")
            return True
        else:
            print("❌ Basic test failed")
            print(result.stdout)
            print(result.stderr)
            return False
    except FileNotFoundError:
        print("⚠️  test_basic.py not found, skipping test")
        return True

def launch_application():
    """Launch the main application."""
    print("\nLaunching SIMAVER...")
    try:
        subprocess.run([sys.executable, 'main.py'])
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"❌ Failed to launch application: {e}")

def main():
    """Main launcher function."""
    print("SIMAVER Launcher")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        input("Press Enter to exit...")
        return 1
    
    # Check tkinter
    if not check_tkinter():
        input("Press Enter to exit...")
        return 1
    
    # Check critical dependencies
    if not check_critical_dependencies():
        input("Press Enter to exit...")
        return 1
    
    # Run basic test
    if not run_basic_test():
        response = input("Basic test failed. Continue anyway? (y/n): ").lower().strip()
        if response not in ['y', 'yes']:
            return 1
    
    # Launch application
    launch_application()
    return 0

if __name__ == "__main__":
    sys.exit(main())
