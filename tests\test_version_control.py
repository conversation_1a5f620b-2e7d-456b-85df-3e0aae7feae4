"""
Unit tests for version control functionality.

Tests the core version control operations including project management,
backup creation, and file monitoring integration.
"""

import unittest
import tempfile
import shutil
from pathlib import Path
from datetime import datetime
import logging

# Disable logging during tests
logging.disable(logging.CRITICAL)

from logic.version_control import VersionControl
from logic.project_scanner import SimaticProject
from config.settings import Settings


class TestVersionControl(unittest.TestCase):
    """Test cases for VersionControl class."""
    
    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for tests
        self.test_dir = Path(tempfile.mkdtemp())
        self.backup_dir = self.test_dir / 'backups'
        self.projects_dir = self.test_dir / 'projects'
        
        # Create test directories
        self.backup_dir.mkdir(parents=True)
        self.projects_dir.mkdir(parents=True)
        
        # Create test settings
        self.test_settings = Settings(str(self.test_dir / 'config'))
        self.test_settings.set('paths', 'backup_dir', str(self.backup_dir))
        
        # Initialize version control with test settings
        self.vc = VersionControl()
        self.vc.backup_manager.backup_root = self.backup_dir
        
        # Create test project
        self.test_project = self._create_test_project()
    
    def tearDown(self):
        """Clean up test environment."""
        # Shutdown version control
        self.vc.shutdown()
        
        # Remove temporary directory
        shutil.rmtree(self.test_dir, ignore_errors=True)
        
        # Re-enable logging
        logging.disable(logging.NOTSET)
    
    def _create_test_project(self) -> SimaticProject:
        """Create a test Simatic project."""
        project_dir = self.projects_dir / 'TestProject'
        project_dir.mkdir()
        
        # Create test project file
        project_file = project_dir / 'TestProject.s7p'
        project_file.write_text('Test project content')
        
        # Create additional files
        (project_dir / 'program.awl').write_text('AWL program content')
        (project_dir / 'function.scl').write_text('SCL function content')
        
        return SimaticProject(
            name='TestProject',
            path=project_dir,
            project_file=project_file,
            project_type='STEP7',
            last_modified=datetime.now()
        )
    
    def test_add_project(self):
        """Test adding a project to version control."""
        # Add project
        self.vc.add_project(self.test_project, start_monitoring=False)
        
        # Verify project was added
        self.assertIn('TestProject', self.vc.monitored_projects)
        self.assertEqual(
            self.vc.monitored_projects['TestProject'].name,
            'TestProject'
        )
    
    def test_remove_project(self):
        """Test removing a project from version control."""
        # Add and then remove project
        self.vc.add_project(self.test_project, start_monitoring=False)
        self.vc.remove_project('TestProject')
        
        # Verify project was removed
        self.assertNotIn('TestProject', self.vc.monitored_projects)
    
    def test_create_backup(self):
        """Test creating a backup."""
        # Add project
        self.vc.add_project(self.test_project, start_monitoring=False)
        
        # Create backup
        backup_info = self.vc.create_backup('TestProject', 'Test backup')
        
        # Verify backup was created
        self.assertIsNotNone(backup_info)
        self.assertEqual(backup_info.project_name, 'TestProject')
        self.assertEqual(backup_info.description, 'Test backup')
        self.assertTrue(Path(backup_info.backup_path).exists())
    
    def test_get_project_status(self):
        """Test getting project status."""
        # Add project
        self.vc.add_project(self.test_project, start_monitoring=False)
        
        # Get status
        status = self.vc.get_project_status('TestProject')
        
        # Verify status
        self.assertIsNotNone(status)
        self.assertEqual(status.project.name, 'TestProject')
        self.assertFalse(status.has_changes)  # No changes initially
        self.assertFalse(status.monitoring_active)  # Monitoring not started
    
    def test_get_all_project_statuses(self):
        """Test getting all project statuses."""
        # Add multiple projects
        self.vc.add_project(self.test_project, start_monitoring=False)
        
        # Create second test project
        project2 = self._create_test_project()
        project2.name = 'TestProject2'
        project2.path = self.projects_dir / 'TestProject2'
        project2.path.mkdir()
        (project2.path / 'TestProject2.s7p').write_text('Test content 2')
        
        self.vc.add_project(project2, start_monitoring=False)
        
        # Get all statuses
        statuses = self.vc.get_all_project_statuses()
        
        # Verify statuses
        self.assertEqual(len(statuses), 2)
        self.assertIn('TestProject', statuses)
        self.assertIn('TestProject2', statuses)
    
    def test_backup_cleanup(self):
        """Test backup cleanup functionality."""
        # Add project
        self.vc.add_project(self.test_project, start_monitoring=False)
        
        # Create multiple backups
        backups = []
        for i in range(5):
            backup = self.vc.create_backup('TestProject', f'Backup {i}')
            backups.append(backup)
        
        # Cleanup old backups (keep only 3)
        deleted_count = self.vc.backup_manager.cleanup_old_backups('TestProject', 3)
        
        # Verify cleanup
        self.assertEqual(deleted_count, 2)  # Should delete 2 old backups
        
        # Verify remaining backups
        remaining_backups = self.vc.backup_manager.list_backups('TestProject')
        self.assertEqual(len(remaining_backups), 3)
    
    def test_statistics(self):
        """Test getting version control statistics."""
        # Add project and create backup
        self.vc.add_project(self.test_project, start_monitoring=False)
        self.vc.create_backup('TestProject', 'Test backup')
        
        # Get statistics
        stats = self.vc.get_statistics()
        
        # Verify statistics
        self.assertEqual(stats['monitored_projects'], 1)
        self.assertEqual(stats['total_backups'], 1)
        self.assertGreater(stats['total_backup_size'], 0)
        self.assertFalse(stats['monitoring_active'])


class TestVersionControlIntegration(unittest.TestCase):
    """Integration tests for version control with file monitoring."""
    
    def setUp(self):
        """Set up test environment."""
        self.test_dir = Path(tempfile.mkdtemp())
        self.backup_dir = self.test_dir / 'backups'
        self.projects_dir = self.test_dir / 'projects'
        
        self.backup_dir.mkdir(parents=True)
        self.projects_dir.mkdir(parents=True)
        
        self.vc = VersionControl()
        self.vc.backup_manager.backup_root = self.backup_dir
    
    def tearDown(self):
        """Clean up test environment."""
        self.vc.shutdown()
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_file_change_tracking(self):
        """Test file change tracking integration."""
        # Create test project
        project_dir = self.projects_dir / 'TestProject'
        project_dir.mkdir()
        project_file = project_dir / 'TestProject.s7p'
        project_file.write_text('Initial content')
        
        project = SimaticProject(
            name='TestProject',
            path=project_dir,
            project_file=project_file,
            project_type='STEP7',
            last_modified=datetime.now()
        )
        
        # Add project (without starting monitoring for this test)
        self.vc.add_project(project, start_monitoring=False)
        
        # Simulate file change event
        from logic.file_monitor import FileChangeEvent
        change_event = FileChangeEvent(
            event_type='modified',
            file_path=project_file,
            timestamp=datetime.now(),
            project_name='TestProject'
        )
        
        # Process change event
        self.vc._on_file_change(change_event)
        
        # Verify change was tracked
        status = self.vc.get_project_status('TestProject')
        self.assertTrue(status.has_changes)
        self.assertEqual(status.change_count, 1)


if __name__ == '__main__':
    unittest.main()
