@echo off
REM SIMAVER Installation Script for Windows
REM This script automates the installation of dependencies for SIMAVER

echo ========================================
echo SIMAVER - Simatic Manager Version Control
echo Installation Script for Windows
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.12 or later from https://python.org
    pause
    exit /b 1
)

echo Python found. Checking version...
python -c "import sys; print(f'Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}')"

REM Check if conda is available
conda --version >nul 2>&1
if %errorlevel% equ 0 (
    echo.
    echo Conda detected. Would you like to create a new conda environment? [Y/N]
    set /p use_conda=
    if /i "%use_conda%"=="Y" (
        echo Creating conda environment 'simaver' with Python 3.12...
        conda create -n simaver python=3.12 -y
        if %errorlevel% neq 0 (
            echo ERROR: Failed to create conda environment
            pause
            exit /b 1
        )
        echo Activating conda environment...
        call conda activate simaver
    )
)

echo.
echo Installing Python dependencies...
pip install --upgrade pip
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo Creating application directories...
if not exist "ui" mkdir ui
if not exist "logic" mkdir logic
if not exist "assets" mkdir assets
if not exist "assets\icons" mkdir assets\icons
if not exist "assets\styles" mkdir assets\styles
if not exist "config" mkdir config
if not exist "tests" mkdir tests
if not exist "data" mkdir data
if not exist "backups" mkdir backups

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To run SIMAVER:
if /i "%use_conda%"=="Y" (
    echo 1. conda activate simaver
    echo 2. python main.py
) else (
    echo python main.py
)
echo.
pause
