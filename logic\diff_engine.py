"""
File comparison and diff engine for SIMAVER application.

This module provides functionality to compare files and directories,
generate diffs, and analyze changes between different versions.
"""

import difflib
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Iterator
from dataclasses import dataclass
from enum import Enum
import hashlib
import mimetypes


class ChangeType(Enum):
    """Types of changes detected in file comparison."""
    ADDED = "added"
    DELETED = "deleted"
    MODIFIED = "modified"
    RENAMED = "renamed"
    UNCHANGED = "unchanged"


@dataclass
class FileDiff:
    """
    Represents differences between two files.
    
    Contains detailed information about changes between file versions.
    """
    file_path: str
    change_type: ChangeType
    old_path: Optional[str] = None
    lines_added: int = 0
    lines_deleted: int = 0
    lines_modified: int = 0
    similarity: float = 0.0  # 0.0 to 1.0
    diff_content: Optional[str] = None
    binary_file: bool = False
    
    @property
    def total_changes(self) -> int:
        """Get total number of changed lines."""
        return self.lines_added + self.lines_deleted + self.lines_modified


@dataclass
class DirectoryDiff:
    """
    Represents differences between two directories.
    
    Contains summary of all file changes in a directory comparison.
    """
    added_files: List[str]
    deleted_files: List[str]
    modified_files: List[FileDiff]
    renamed_files: List[Tuple[str, str]]  # (old_path, new_path)
    unchanged_files: List[str]
    
    @property
    def total_changes(self) -> int:
        """Get total number of file changes."""
        return (len(self.added_files) + len(self.deleted_files) + 
                len(self.modified_files) + len(self.renamed_files))


class DiffEngine:
    """
    Engine for comparing files and directories.
    
    Provides comprehensive file and directory comparison functionality
    with support for text and binary files.
    """
    
    def __init__(self):
        """Initialize diff engine."""
        self.logger = logging.getLogger(__name__)
        
        # Configuration
        self.ignore_whitespace = False
        self.ignore_case = False
        self.context_lines = 3
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        
        # File type detection
        self.text_extensions = {
            '.awl', '.scl', '.fbd', '.lad', '.txt', '.xml', '.json', 
            '.ini', '.cfg', '.log', '.csv', '.sql', '.py', '.js', '.html'
        }
        
        self.binary_extensions = {
            '.s7p', '.s7l', '.ap15', '.ap16', '.ap17', '.ap18', '.s5d',
            '.exe', '.dll', '.zip', '.rar', '.pdf', '.doc', '.docx'
        }
    
    def compare_files(self, file1: Path, file2: Path) -> FileDiff:
        """
        Compare two files and generate diff information.
        
        Args:
            file1: First file to compare
            file2: Second file to compare
            
        Returns:
            FileDiff object with comparison results
        """
        self.logger.debug(f"Comparing files: {file1} vs {file2}")
        
        # Check if files exist
        if not file1.exists():
            return FileDiff(
                file_path=str(file2),
                change_type=ChangeType.ADDED,
                lines_added=self._count_lines(file2) if file2.exists() else 0
            )
        
        if not file2.exists():
            return FileDiff(
                file_path=str(file1),
                change_type=ChangeType.DELETED,
                lines_deleted=self._count_lines(file1)
            )
        
        # Check if files are identical
        if self._files_identical(file1, file2):
            return FileDiff(
                file_path=str(file2),
                change_type=ChangeType.UNCHANGED,
                similarity=1.0
            )
        
        # Determine if files are binary
        is_binary = self._is_binary_file(file1) or self._is_binary_file(file2)
        
        if is_binary:
            return self._compare_binary_files(file1, file2)
        else:
            return self._compare_text_files(file1, file2)
    
    def compare_directories(self, dir1: Path, dir2: Path) -> DirectoryDiff:
        """
        Compare two directories and generate diff information.
        
        Args:
            dir1: First directory to compare
            dir2: Second directory to compare
            
        Returns:
            DirectoryDiff object with comparison results
        """
        self.logger.info(f"Comparing directories: {dir1} vs {dir2}")
        
        # Get all files in both directories
        files1 = self._get_directory_files(dir1) if dir1.exists() else set()
        files2 = self._get_directory_files(dir2) if dir2.exists() else set()
        
        # Find added, deleted, and common files
        added_files = list(files2 - files1)
        deleted_files = list(files1 - files2)
        common_files = files1 & files2
        
        # Compare common files
        modified_files = []
        unchanged_files = []
        
        for rel_path in common_files:
            file1 = dir1 / rel_path
            file2 = dir2 / rel_path
            
            file_diff = self.compare_files(file1, file2)
            
            if file_diff.change_type == ChangeType.UNCHANGED:
                unchanged_files.append(rel_path)
            else:
                file_diff.file_path = rel_path
                file_diff.change_type = ChangeType.MODIFIED
                modified_files.append(file_diff)
        
        # Detect renamed files (simple heuristic based on content similarity)
        renamed_files = self._detect_renamed_files(
            dir1, dir2, added_files, deleted_files
        )
        
        # Remove renamed files from added/deleted lists
        for old_path, new_path in renamed_files:
            if old_path in deleted_files:
                deleted_files.remove(old_path)
            if new_path in added_files:
                added_files.remove(new_path)
        
        return DirectoryDiff(
            added_files=sorted(added_files),
            deleted_files=sorted(deleted_files),
            modified_files=modified_files,
            renamed_files=renamed_files,
            unchanged_files=sorted(unchanged_files)
        )
    
    def generate_unified_diff(self, file1: Path, file2: Path, 
                            context_lines: Optional[int] = None) -> str:
        """
        Generate unified diff format for two text files.
        
        Args:
            file1: First file
            file2: Second file
            context_lines: Number of context lines (uses default if None)
            
        Returns:
            Unified diff as string
        """
        if context_lines is None:
            context_lines = self.context_lines
        
        try:
            # Read file contents
            with open(file1, 'r', encoding='utf-8', errors='ignore') as f:
                lines1 = f.readlines()
            
            with open(file2, 'r', encoding='utf-8', errors='ignore') as f:
                lines2 = f.readlines()
            
            # Apply preprocessing if configured
            if self.ignore_whitespace:
                lines1 = [line.strip() + '\n' for line in lines1]
                lines2 = [line.strip() + '\n' for line in lines2]
            
            if self.ignore_case:
                lines1 = [line.lower() for line in lines1]
                lines2 = [line.lower() for line in lines2]
            
            # Generate unified diff
            diff = difflib.unified_diff(
                lines1, lines2,
                fromfile=str(file1),
                tofile=str(file2),
                n=context_lines
            )
            
            return ''.join(diff)
        
        except Exception as e:
            self.logger.error(f"Error generating unified diff: {e}")
            return f"Error generating diff: {e}"
    
    def _compare_text_files(self, file1: Path, file2: Path) -> FileDiff:
        """Compare two text files."""
        try:
            # Read file contents
            with open(file1, 'r', encoding='utf-8', errors='ignore') as f:
                lines1 = f.readlines()
            
            with open(file2, 'r', encoding='utf-8', errors='ignore') as f:
                lines2 = f.readlines()
            
            # Apply preprocessing
            if self.ignore_whitespace:
                lines1 = [line.strip() for line in lines1]
                lines2 = [line.strip() for line in lines2]
            
            if self.ignore_case:
                lines1 = [line.lower() for line in lines1]
                lines2 = [line.lower() for line in lines2]
            
            # Calculate similarity
            similarity = difflib.SequenceMatcher(None, lines1, lines2).ratio()
            
            # Count changes
            differ = difflib.Differ()
            diff = list(differ.compare(lines1, lines2))
            
            lines_added = sum(1 for line in diff if line.startswith('+ '))
            lines_deleted = sum(1 for line in diff if line.startswith('- '))
            
            # Generate diff content
            diff_content = self.generate_unified_diff(file1, file2)
            
            return FileDiff(
                file_path=str(file2),
                change_type=ChangeType.MODIFIED,
                lines_added=lines_added,
                lines_deleted=lines_deleted,
                similarity=similarity,
                diff_content=diff_content
            )
        
        except Exception as e:
            self.logger.error(f"Error comparing text files: {e}")
            return FileDiff(
                file_path=str(file2),
                change_type=ChangeType.MODIFIED,
                similarity=0.0
            )
    
    def _compare_binary_files(self, file1: Path, file2: Path) -> FileDiff:
        """Compare two binary files."""
        try:
            # For binary files, we can only check if they're identical
            hash1 = self._calculate_file_hash(file1)
            hash2 = self._calculate_file_hash(file2)
            
            if hash1 == hash2:
                similarity = 1.0
                change_type = ChangeType.UNCHANGED
            else:
                similarity = 0.0
                change_type = ChangeType.MODIFIED
            
            return FileDiff(
                file_path=str(file2),
                change_type=change_type,
                similarity=similarity,
                binary_file=True,
                diff_content="Binary files differ" if similarity == 0.0 else None
            )
        
        except Exception as e:
            self.logger.error(f"Error comparing binary files: {e}")
            return FileDiff(
                file_path=str(file2),
                change_type=ChangeType.MODIFIED,
                binary_file=True,
                similarity=0.0
            )
    
    def _files_identical(self, file1: Path, file2: Path) -> bool:
        """Check if two files are identical."""
        try:
            # Quick size check first
            if file1.stat().st_size != file2.stat().st_size:
                return False
            
            # Compare file hashes
            return self._calculate_file_hash(file1) == self._calculate_file_hash(file2)
        
        except Exception:
            return False
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate MD5 hash of file."""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def _is_binary_file(self, file_path: Path) -> bool:
        """Determine if file is binary."""
        # Check by extension first
        ext = file_path.suffix.lower()
        if ext in self.binary_extensions:
            return True
        if ext in self.text_extensions:
            return False
        
        # Use mimetypes module
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if mime_type:
            return not mime_type.startswith('text/')
        
        # Check file content (sample first 1024 bytes)
        try:
            with open(file_path, 'rb') as f:
                chunk = f.read(1024)
                return b'\0' in chunk
        except Exception:
            return True  # Assume binary if can't read
    
    def _count_lines(self, file_path: Path) -> int:
        """Count lines in a text file."""
        try:
            if self._is_binary_file(file_path):
                return 0
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                return sum(1 for _ in f)
        except Exception:
            return 0
    
    def _get_directory_files(self, directory: Path) -> set:
        """Get set of relative file paths in directory."""
        files = set()
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    rel_path = file_path.relative_to(directory)
                    files.add(str(rel_path))
        except Exception as e:
            self.logger.error(f"Error getting directory files: {e}")
        
        return files
    
    def _detect_renamed_files(self, dir1: Path, dir2: Path, 
                            added_files: List[str], 
                            deleted_files: List[str]) -> List[Tuple[str, str]]:
        """
        Detect renamed files based on content similarity.
        
        Returns list of (old_path, new_path) tuples.
        """
        renamed_files = []
        similarity_threshold = 0.8
        
        # Compare each deleted file with each added file
        for deleted_file in deleted_files[:]:  # Copy list to avoid modification issues
            best_match = None
            best_similarity = 0.0
            
            for added_file in added_files[:]:
                try:
                    file1 = dir1 / deleted_file
                    file2 = dir2 / added_file
                    
                    # Quick size check
                    if abs(file1.stat().st_size - file2.stat().st_size) > 1024:
                        continue
                    
                    # Compare content
                    if self._is_binary_file(file1) or self._is_binary_file(file2):
                        # For binary files, use hash comparison
                        if self._calculate_file_hash(file1) == self._calculate_file_hash(file2):
                            similarity = 1.0
                        else:
                            similarity = 0.0
                    else:
                        # For text files, use sequence matcher
                        with open(file1, 'r', encoding='utf-8', errors='ignore') as f:
                            content1 = f.read()
                        with open(file2, 'r', encoding='utf-8', errors='ignore') as f:
                            content2 = f.read()
                        
                        similarity = difflib.SequenceMatcher(None, content1, content2).ratio()
                    
                    if similarity > best_similarity:
                        best_similarity = similarity
                        best_match = added_file
                
                except Exception as e:
                    self.logger.debug(f"Error comparing {deleted_file} with {added_file}: {e}")
                    continue
            
            # If we found a good match, consider it a rename
            if best_match and best_similarity >= similarity_threshold:
                renamed_files.append((deleted_file, best_match))
        
        return renamed_files
