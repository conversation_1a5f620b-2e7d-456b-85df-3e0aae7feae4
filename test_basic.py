#!/usr/bin/env python3
"""
Basic functionality test for SIMAVER without external dependencies.

This script tests core functionality using only built-in Python modules.
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, '.')

def test_basic_imports():
    """Test that basic modules can be imported."""
    print("Testing basic imports...")
    
    try:
        from config.settings import Settings
        print("✅ Settings module imported")
    except Exception as e:
        print(f"❌ Settings import failed: {e}")
        return False
    
    try:
        from logic.project_scanner import SimaticProject, ProjectScanner
        print("✅ Project scanner imported")
    except Exception as e:
        print(f"❌ Project scanner import failed: {e}")
        return False
    
    try:
        from logic.diff_engine import DiffEngine, ChangeType
        print("✅ Diff engine imported")
    except Exception as e:
        print(f"❌ Diff engine import failed: {e}")
        return False
    
    return True

def test_settings():
    """Test settings functionality."""
    print("\nTesting settings...")
    
    try:
        # Create temporary config directory
        temp_dir = Path(tempfile.mkdtemp())
        
        # Test settings creation
        settings = Settings(str(temp_dir))
        
        # Test setting and getting values
        settings.set('test', 'value', 'hello')
        value = settings.get('test', 'value')
        
        if value == 'hello':
            print("✅ Settings get/set works")
        else:
            print(f"❌ Settings get/set failed: expected 'hello', got '{value}'")
            return False
        
        # Test saving and loading
        settings.save_settings()
        
        # Create new settings instance and load
        settings2 = Settings(str(temp_dir))
        value2 = settings2.get('test', 'value')
        
        if value2 == 'hello':
            print("✅ Settings persistence works")
        else:
            print(f"❌ Settings persistence failed: expected 'hello', got '{value2}'")
            return False
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False
    
    return True

def test_project_scanner():
    """Test project scanner functionality."""
    print("\nTesting project scanner...")
    
    try:
        from logic.project_scanner import ProjectScanner, SimaticProject
        
        # Create temporary test directory
        temp_dir = Path(tempfile.mkdtemp())
        
        # Create test project structure
        project_dir = temp_dir / 'TestProject'
        project_dir.mkdir()
        
        # Create STEP 7 project file
        project_file = project_dir / 'TestProject.s7p'
        project_file.write_text('Test project content')
        
        # Create additional files
        (project_dir / 'program.awl').write_text('AWL content')
        (project_dir / 'function.scl').write_text('SCL content')
        
        # Test scanner
        scanner = ProjectScanner()
        projects = scanner.scan_directory(temp_dir)
        
        if len(projects) == 1:
            project = projects[0]
            if project.name == 'TestProject' and project.project_type == 'STEP7':
                print("✅ Project scanner works")
            else:
                print(f"❌ Project scanner failed: wrong project details")
                return False
        else:
            print(f"❌ Project scanner failed: expected 1 project, found {len(projects)}")
            return False
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        
    except Exception as e:
        print(f"❌ Project scanner test failed: {e}")
        return False
    
    return True

def test_diff_engine():
    """Test diff engine functionality."""
    print("\nTesting diff engine...")
    
    try:
        from logic.diff_engine import DiffEngine, ChangeType
        
        # Create temporary test files
        temp_dir = Path(tempfile.mkdtemp())
        
        file1 = temp_dir / 'file1.txt'
        file2 = temp_dir / 'file2.txt'
        
        # Test identical files
        content = "Line 1\nLine 2\nLine 3\n"
        file1.write_text(content)
        file2.write_text(content)
        
        diff_engine = DiffEngine()
        diff = diff_engine.compare_files(file1, file2)
        
        if diff.change_type == ChangeType.UNCHANGED:
            print("✅ Diff engine detects identical files")
        else:
            print(f"❌ Diff engine failed: expected UNCHANGED, got {diff.change_type}")
            return False
        
        # Test modified files
        file2.write_text("Line 1\nModified Line 2\nLine 3\n")
        diff = diff_engine.compare_files(file1, file2)
        
        if diff.change_type == ChangeType.MODIFIED:
            print("✅ Diff engine detects modified files")
        else:
            print(f"❌ Diff engine failed: expected MODIFIED, got {diff.change_type}")
            return False
        
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)
        
    except Exception as e:
        print(f"❌ Diff engine test failed: {e}")
        return False
    
    return True

def test_gui_imports():
    """Test GUI module imports."""
    print("\nTesting GUI imports...")
    
    try:
        import tkinter as tk
        print("✅ tkinter available")
    except Exception as e:
        print(f"❌ tkinter not available: {e}")
        return False
    
    try:
        from tkinter import ttk
        print("✅ ttk available")
    except Exception as e:
        print(f"❌ ttk not available: {e}")
        return False
    
    # Test basic GUI creation (without showing)
    try:
        root = tk.Tk()
        root.withdraw()  # Hide window
        root.title("Test")
        root.destroy()
        print("✅ Basic GUI creation works")
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        return False
    
    return True

def main():
    """Run all basic tests."""
    print("SIMAVER Basic Functionality Test")
    print("=" * 40)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("Settings", test_settings),
        ("Project Scanner", test_project_scanner),
        ("Diff Engine", test_diff_engine),
        ("GUI Imports", test_gui_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 40)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✅ All basic tests PASSED!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install watchdog psutil pillow")
        print("2. Run full application: python main.py")
        return True
    else:
        print("❌ Some tests FAILED!")
        print("Please check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
