"""
File system monitoring for SIMAVER application.

This module provides real-time monitoring of Simatic project files and directories
using the watchdog library for cross-platform file system events.
"""

import logging
import threading
from pathlib import Path
from typing import Dict, List, Callable, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileSystemEvent

from .project_scanner import SimaticProject


@dataclass
class FileChangeEvent:
    """
    Represents a file change event.
    
    Contains information about a detected file system change.
    """
    event_type: str  # 'created', 'modified', 'deleted', 'moved'
    file_path: Path
    timestamp: datetime
    project_name: Optional[str] = None
    old_path: Optional[Path] = None  # For move events
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'event_type': self.event_type,
            'file_path': str(self.file_path),
            'timestamp': self.timestamp.isoformat(),
            'project_name': self.project_name,
            'old_path': str(self.old_path) if self.old_path else None
        }


class ProjectFileHandler(FileSystemEventHandler):
    """
    File system event handler for Simatic projects.
    
    Processes file system events and filters them for relevant project files.
    """
    
    def __init__(self, monitor: 'FileMonitor'):
        """
        Initialize file handler.
        
        Args:
            monitor: Parent FileMonitor instance
        """
        super().__init__()
        self.monitor = monitor
        self.logger = logging.getLogger(__name__)
    
    def on_created(self, event: FileSystemEvent) -> None:
        """Handle file/directory creation events."""
        if not event.is_directory:
            self._process_event('created', Path(event.src_path))
    
    def on_modified(self, event: FileSystemEvent) -> None:
        """Handle file/directory modification events."""
        if not event.is_directory:
            self._process_event('modified', Path(event.src_path))
    
    def on_deleted(self, event: FileSystemEvent) -> None:
        """Handle file/directory deletion events."""
        if not event.is_directory:
            self._process_event('deleted', Path(event.src_path))
    
    def on_moved(self, event: FileSystemEvent) -> None:
        """Handle file/directory move events."""
        if not event.is_directory:
            self._process_event('moved', Path(event.dest_path), Path(event.src_path))
    
    def _process_event(self, event_type: str, file_path: Path, 
                      old_path: Optional[Path] = None) -> None:
        """
        Process a file system event.
        
        Args:
            event_type: Type of event
            file_path: Path of affected file
            old_path: Original path for move events
        """
        # Check if file is relevant to monitored projects
        if self.monitor._is_relevant_file(file_path):
            project_name = self.monitor._get_project_for_file(file_path)
            
            change_event = FileChangeEvent(
                event_type=event_type,
                file_path=file_path,
                timestamp=datetime.now(),
                project_name=project_name,
                old_path=old_path
            )
            
            self.monitor._handle_change_event(change_event)


class FileMonitor:
    """
    Monitors file system changes for Simatic projects.
    
    Provides real-time monitoring of project directories and files,
    with configurable filtering and event handling.
    """
    
    def __init__(self):
        """Initialize file monitor."""
        self.logger = logging.getLogger(__name__)
        self.observer = Observer()
        self.handler = ProjectFileHandler(self)
        
        # Monitoring state
        self.is_monitoring = False
        self.monitored_paths: Dict[str, Path] = {}  # project_name -> path
        self.monitored_projects: Dict[str, SimaticProject] = {}
        
        # Event handling
        self.change_callbacks: List[Callable[[FileChangeEvent], None]] = []
        self.recent_events: List[FileChangeEvent] = []
        self.max_recent_events = 1000
        
        # File filtering
        self.monitored_extensions = {'.s7p', '.s7l', '.awl', '.scl', '.fbd', '.lad', 
                                   '.ap15', '.ap16', '.ap17', '.ap18', '.s5d', '.mcp'}
        self.ignore_patterns = {'*.tmp', '*.bak', '*~', '*.log'}
        
        # Debouncing for rapid file changes
        self.debounce_interval = timedelta(seconds=1)
        self.pending_events: Dict[str, FileChangeEvent] = {}
        self.debounce_timer: Optional[threading.Timer] = None
        self._lock = threading.Lock()
    
    def add_project(self, project: SimaticProject) -> None:
        """
        Add a project to monitoring.
        
        Args:
            project: SimaticProject to monitor
        """
        self.logger.info(f"Adding project to monitoring: {project.name}")
        
        with self._lock:
            self.monitored_projects[project.name] = project
            self.monitored_paths[project.name] = project.path
            
            if self.is_monitoring:
                # Add watch for this project path
                try:
                    self.observer.schedule(
                        self.handler, 
                        str(project.path), 
                        recursive=True
                    )
                    self.logger.info(f"Started monitoring: {project.path}")
                except Exception as e:
                    self.logger.error(f"Error adding watch for {project.path}: {e}")
    
    def remove_project(self, project_name: str) -> None:
        """
        Remove a project from monitoring.
        
        Args:
            project_name: Name of project to stop monitoring
        """
        self.logger.info(f"Removing project from monitoring: {project_name}")
        
        with self._lock:
            if project_name in self.monitored_projects:
                del self.monitored_projects[project_name]
            
            if project_name in self.monitored_paths:
                project_path = self.monitored_paths[project_name]
                del self.monitored_paths[project_name]
                
                if self.is_monitoring:
                    # Remove watch (requires stopping and restarting observer)
                    self._restart_observer()
    
    def start_monitoring(self) -> None:
        """Start file system monitoring."""
        if self.is_monitoring:
            self.logger.warning("File monitoring is already active")
            return
        
        self.logger.info("Starting file system monitoring")
        
        try:
            # Add watches for all monitored projects
            for project_name, project_path in self.monitored_paths.items():
                self.observer.schedule(
                    self.handler, 
                    str(project_path), 
                    recursive=True
                )
                self.logger.debug(f"Added watch for: {project_path}")
            
            self.observer.start()
            self.is_monitoring = True
            self.logger.info("File system monitoring started")
        
        except Exception as e:
            self.logger.error(f"Error starting file monitoring: {e}")
            raise
    
    def stop_monitoring(self) -> None:
        """Stop file system monitoring."""
        if not self.is_monitoring:
            return
        
        self.logger.info("Stopping file system monitoring")
        
        try:
            self.observer.stop()
            self.observer.join(timeout=5.0)
            self.is_monitoring = False
            
            # Cancel any pending debounce timer
            if self.debounce_timer:
                self.debounce_timer.cancel()
                self.debounce_timer = None
            
            self.logger.info("File system monitoring stopped")
        
        except Exception as e:
            self.logger.error(f"Error stopping file monitoring: {e}")
    
    def _restart_observer(self) -> None:
        """Restart the observer with current monitored paths."""
        if self.is_monitoring:
            self.stop_monitoring()
            # Create new observer
            self.observer = Observer()
            self.handler = ProjectFileHandler(self)
            self.start_monitoring()
    
    def add_change_callback(self, callback: Callable[[FileChangeEvent], None]) -> None:
        """
        Add callback for file change events.
        
        Args:
            callback: Function to call when file changes occur
        """
        self.change_callbacks.append(callback)
    
    def remove_change_callback(self, callback: Callable[[FileChangeEvent], None]) -> None:
        """
        Remove change callback.
        
        Args:
            callback: Callback function to remove
        """
        if callback in self.change_callbacks:
            self.change_callbacks.remove(callback)
    
    def _is_relevant_file(self, file_path: Path) -> bool:
        """
        Check if file is relevant for monitoring.
        
        Args:
            file_path: File path to check
            
        Returns:
            True if file should be monitored
        """
        # Check file extension
        if file_path.suffix.lower() not in self.monitored_extensions:
            return False
        
        # Check ignore patterns
        file_name = file_path.name.lower()
        for pattern in self.ignore_patterns:
            if pattern.startswith('*'):
                if file_name.endswith(pattern[1:]):
                    return False
            elif pattern.endswith('*'):
                if file_name.startswith(pattern[:-1]):
                    return False
            elif pattern in file_name:
                return False
        
        return True
    
    def _get_project_for_file(self, file_path: Path) -> Optional[str]:
        """
        Get project name for a file path.
        
        Args:
            file_path: File path to check
            
        Returns:
            Project name if file belongs to monitored project
        """
        for project_name, project_path in self.monitored_paths.items():
            try:
                # Check if file is within project directory
                file_path.relative_to(project_path)
                return project_name
            except ValueError:
                continue
        
        return None
    
    def _handle_change_event(self, event: FileChangeEvent) -> None:
        """
        Handle a file change event with debouncing.
        
        Args:
            event: FileChangeEvent to handle
        """
        with self._lock:
            # Use file path as key for debouncing
            event_key = str(event.file_path)
            
            # Store the latest event for this file
            self.pending_events[event_key] = event
            
            # Cancel existing timer and start new one
            if self.debounce_timer:
                self.debounce_timer.cancel()
            
            self.debounce_timer = threading.Timer(
                self.debounce_interval.total_seconds(),
                self._process_pending_events
            )
            self.debounce_timer.start()
    
    def _process_pending_events(self) -> None:
        """Process all pending events after debounce period."""
        with self._lock:
            events_to_process = list(self.pending_events.values())
            self.pending_events.clear()
            self.debounce_timer = None
        
        # Process each event
        for event in events_to_process:
            self._process_change_event(event)
    
    def _process_change_event(self, event: FileChangeEvent) -> None:
        """
        Process a single change event.
        
        Args:
            event: FileChangeEvent to process
        """
        self.logger.debug(f"Processing file change: {event.event_type} - {event.file_path}")
        
        # Add to recent events
        self.recent_events.append(event)
        if len(self.recent_events) > self.max_recent_events:
            self.recent_events.pop(0)
        
        # Notify callbacks
        for callback in self.change_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"Error in change callback: {e}")
    
    def get_recent_events(self, project_name: Optional[str] = None, 
                         limit: int = 100) -> List[FileChangeEvent]:
        """
        Get recent file change events.
        
        Args:
            project_name: Optional project name filter
            limit: Maximum number of events to return
            
        Returns:
            List of recent FileChangeEvent objects
        """
        events = self.recent_events.copy()
        
        if project_name:
            events = [e for e in events if e.project_name == project_name]
        
        # Return most recent events first
        events.reverse()
        return events[:limit]
    
    def clear_recent_events(self) -> None:
        """Clear the recent events list."""
        with self._lock:
            self.recent_events.clear()
    
    def get_monitoring_status(self) -> Dict:
        """Get current monitoring status."""
        return {
            'is_monitoring': self.is_monitoring,
            'monitored_projects': list(self.monitored_projects.keys()),
            'monitored_paths': {name: str(path) for name, path in self.monitored_paths.items()},
            'recent_events_count': len(self.recent_events),
            'pending_events_count': len(self.pending_events)
        }
