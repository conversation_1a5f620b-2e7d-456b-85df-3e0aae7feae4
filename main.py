#!/usr/bin/env python3
"""
SIMAVER - Simatic Manager Version Control Application

Main entry point for the SIMAVER desktop application.
Provides version control functionality for Simatic Manager PLC projects.

Author: AI Assistant
Version: 1.0
License: MIT
"""

import sys
import os
import logging
import traceback
from pathlib import Path
import tkinter as tk
from tkinter import messagebox

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import application modules
try:
    from config.settings import settings
    from ui.main_window import MainWindow
    from logic.version_control import VersionControl
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all dependencies are installed.")
    print("Run: pip install -r requirements.txt")
    sys.exit(1)


def setup_logging() -> None:
    """
    Setup application logging.
    
    Configures logging to both file and console with appropriate levels
    and formatting for debugging and monitoring.
    """
    # Create logs directory
    log_dir = settings.config_dir / 'logs'
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Configure logging
    log_file = log_dir / 'simaver.log'
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Setup file handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    
    # Setup console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Log startup
    logger = logging.getLogger(__name__)
    logger.info("=" * 60)
    logger.info("SIMAVER - Simatic Manager Version Control")
    logger.info("Application starting...")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Working directory: {os.getcwd()}")
    logger.info(f"Project root: {project_root}")
    logger.info(f"Config directory: {settings.config_dir}")
    logger.info("=" * 60)


def check_dependencies() -> bool:
    """
    Check if all required dependencies are available.
    
    Returns:
        True if all dependencies are available, False otherwise
    """
    logger = logging.getLogger(__name__)
    missing_deps = []
    
    # Check required modules
    required_modules = [
        'watchdog',
        'plyer',
        'pystray',
        'psutil',
        'PIL'  # Pillow
    ]
    
    for module in required_modules:
        try:
            __import__(module)
            logger.debug(f"✓ {module} available")
        except ImportError:
            missing_deps.append(module)
            logger.error(f"✗ {module} not available")
    
    if missing_deps:
        logger.error(f"Missing dependencies: {', '.join(missing_deps)}")
        return False
    
    logger.info("All dependencies are available")
    return True


def create_required_directories() -> None:
    """Create required application directories."""
    logger = logging.getLogger(__name__)
    
    directories = [
        settings.backup_dir,
        settings.temp_dir,
        settings.config_dir / 'logs',
        project_root / 'data',
        project_root / 'assets' / 'icons',
        project_root / 'assets' / 'styles'
    ]
    
    for directory in directories:
        try:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"Created directory: {directory}")
        except Exception as e:
            logger.error(f"Failed to create directory {directory}: {e}")


def handle_exception(exc_type, exc_value, exc_traceback) -> None:
    """
    Global exception handler.
    
    Logs unhandled exceptions and shows error dialog to user.
    """
    if issubclass(exc_type, KeyboardInterrupt):
        # Allow Ctrl+C to work normally
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger = logging.getLogger(__name__)
    
    # Log the exception
    logger.critical(
        "Unhandled exception occurred",
        exc_info=(exc_type, exc_value, exc_traceback)
    )
    
    # Show error dialog if GUI is available
    try:
        error_msg = f"An unexpected error occurred:\n\n{exc_type.__name__}: {exc_value}"
        
        # Try to show GUI error dialog
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        messagebox.showerror(
            "SIMAVER Error",
            f"{error_msg}\n\nPlease check the log file for more details.\n"
            f"Log location: {settings.config_dir / 'logs' / 'simaver.log'}"
        )
        
        root.destroy()
    
    except Exception:
        # If GUI is not available, print to console
        print(f"CRITICAL ERROR: {exc_type.__name__}: {exc_value}")
        print("Check log file for details.")


def show_startup_error(message: str) -> None:
    """
    Show startup error message.
    
    Args:
        message: Error message to display
    """
    try:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("SIMAVER Startup Error", message)
        root.destroy()
    except Exception:
        print(f"STARTUP ERROR: {message}")


def main() -> int:
    """
    Main application entry point.
    
    Returns:
        Exit code (0 for success, non-zero for error)
    """
    try:
        # Setup logging first
        setup_logging()
        logger = logging.getLogger(__name__)
        
        # Install global exception handler
        sys.excepthook = handle_exception
        
        # Check dependencies
        if not check_dependencies():
            error_msg = (
                "Missing required dependencies.\n\n"
                "Please install them using:\n"
                "pip install -r requirements.txt\n\n"
                "Or if using conda:\n"
                "conda create -n simaver python=3.12\n"
                "conda activate simaver\n"
                "pip install -r requirements.txt"
            )
            show_startup_error(error_msg)
            return 1
        
        # Create required directories
        create_required_directories()
        
        # Check if this is the first run
        first_run = not (settings.config_dir / 'settings.json').exists()
        if first_run:
            logger.info("First run detected")
        
        # Initialize and run the main application
        logger.info("Initializing main window...")
        app = MainWindow()
        
        # Show welcome message on first run
        if first_run:
            app.root.after(1000, lambda: messagebox.showinfo(
                "Welcome to SIMAVER",
                "Welcome to SIMAVER - Simatic Manager Version Control!\n\n"
                "To get started:\n"
                "1. Use 'File > Scan for Projects' to find Simatic projects\n"
                "2. Enable file monitoring to track changes\n"
                "3. Configure settings as needed\n\n"
                "Check the Help menu for more information."
            ))
        
        logger.info("Starting application main loop...")
        app.run()
        
        logger.info("Application closed normally")
        return 0
    
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
        return 0
    
    except Exception as e:
        logger.critical(f"Fatal error during startup: {e}")
        logger.critical(traceback.format_exc())
        
        error_msg = (
            f"Failed to start SIMAVER:\n\n{e}\n\n"
            "Please check the log file for more details."
        )
        show_startup_error(error_msg)
        return 1


if __name__ == "__main__":
    # Set working directory to script location
    os.chdir(project_root)
    
    # Run application
    exit_code = main()
    sys.exit(exit_code)
