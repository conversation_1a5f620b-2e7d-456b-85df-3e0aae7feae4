"""
Main window for SIMAVER application.

This module provides the main GUI window with menu bar, toolbar,
status bar, and main content area for the version control interface.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from pathlib import Path
from typing import Optional, Dict, Any
import threading
from datetime import datetime

from config.settings import settings
from logic.version_control import VersionControl, ProjectStatus
from logic.project_scanner import SimaticProject
from .project_manager import ProjectManagerFrame
from .backup_scheduler import BackupSchedulerFrame
from .settings_dialog import SettingsDialog


class MainWindow:
    """
    Main application window for SIMAVER.
    
    Provides the primary user interface with project management,
    backup operations, and monitoring controls.
    """
    
    def __init__(self):
        """Initialize main window."""
        self.logger = logging.getLogger(__name__)
        
        # Initialize version control system
        self.version_control = VersionControl()
        
        # Setup main window
        self.root = tk.Tk()
        self.root.title("SIMAVER - Simatic Manager Version Control")
        self.root.geometry(f"{settings.get('ui', 'window_width', 1200)}x"
                          f"{settings.get('ui', 'window_height', 800)}")
        
        # Window position
        x = settings.get('ui', 'window_x', 100)
        y = settings.get('ui', 'window_y', 100)
        self.root.geometry(f"+{x}+{y}")
        
        # Configure window
        self.root.minsize(800, 600)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Variables
        self.monitoring_active = tk.BooleanVar(value=False)
        self.auto_backup_enabled = tk.BooleanVar(
            value=settings.get('general', 'auto_backup', True)
        )
        
        # UI Components
        self.project_manager: Optional[ProjectManagerFrame] = None
        self.backup_scheduler: Optional[BackupSchedulerFrame] = None
        self.status_bar: Optional[ttk.Frame] = None
        
        # Setup UI
        self._setup_ui()
        self._setup_callbacks()
        
        # Load initial data
        self._load_initial_data()
        
        self.logger.info("Main window initialized")
    
    def _setup_ui(self) -> None:
        """Setup the user interface components."""
        # Create menu bar
        self._create_menu_bar()
        
        # Create toolbar
        self._create_toolbar()
        
        # Create main content area
        self._create_main_content()
        
        # Create status bar
        self._create_status_bar()
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(2, weight=1)  # Main content area
    
    def _create_menu_bar(self) -> None:
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Scan for Projects...", command=self.scan_for_projects)
        file_menu.add_command(label="Add Project...", command=self.add_project_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="Import Settings...", command=self.import_settings)
        file_menu.add_command(label="Export Settings...", command=self.export_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Edit menu
        edit_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Settings...", command=self.show_settings)
        edit_menu.add_separator()
        edit_menu.add_command(label="Clear Recent Events", command=self.clear_recent_events)
        
        # View menu
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Refresh Projects", command=self.refresh_projects)
        view_menu.add_command(label="Show Statistics", command=self.show_statistics)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_checkbutton(
            label="Auto Backup", 
            variable=self.auto_backup_enabled,
            command=self.toggle_auto_backup
        )
        tools_menu.add_checkbutton(
            label="File Monitoring", 
            variable=self.monitoring_active,
            command=self.toggle_monitoring
        )
        tools_menu.add_separator()
        tools_menu.add_command(label="Backup Scheduler...", command=self.show_backup_scheduler)
        tools_menu.add_command(label="Cleanup Old Backups...", command=self.cleanup_backups)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About...", command=self.show_about)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)
    
    def _create_toolbar(self) -> None:
        """Create the application toolbar."""
        toolbar = ttk.Frame(self.root)
        toolbar.grid(row=0, column=0, sticky="ew", padx=5, pady=2)
        
        # Scan button
        ttk.Button(
            toolbar, 
            text="Scan Projects", 
            command=self.scan_for_projects
        ).pack(side=tk.LEFT, padx=2)
        
        # Add project button
        ttk.Button(
            toolbar, 
            text="Add Project", 
            command=self.add_project_dialog
        ).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Monitoring toggle
        ttk.Checkbutton(
            toolbar, 
            text="Monitor Files", 
            variable=self.monitoring_active,
            command=self.toggle_monitoring
        ).pack(side=tk.LEFT, padx=2)
        
        # Auto backup toggle
        ttk.Checkbutton(
            toolbar, 
            text="Auto Backup", 
            variable=self.auto_backup_enabled,
            command=self.toggle_auto_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Refresh button
        ttk.Button(
            toolbar, 
            text="Refresh", 
            command=self.refresh_projects
        ).pack(side=tk.LEFT, padx=2)
        
        # Settings button
        ttk.Button(
            toolbar, 
            text="Settings", 
            command=self.show_settings
        ).pack(side=tk.RIGHT, padx=2)
    
    def _create_main_content(self) -> None:
        """Create the main content area."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.grid(row=2, column=0, sticky="nsew", padx=5, pady=5)
        
        # Project Manager tab
        self.project_manager = ProjectManagerFrame(self.notebook, self.version_control)
        self.notebook.add(self.project_manager, text="Projects")
        
        # Backup Scheduler tab
        self.backup_scheduler = BackupSchedulerFrame(self.notebook, self.version_control)
        self.notebook.add(self.backup_scheduler, text="Backups")
    
    def _create_status_bar(self) -> None:
        """Create the status bar."""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.grid(row=3, column=0, sticky="ew", padx=5, pady=2)
        
        # Status label
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT)
        
        # Progress bar (hidden by default)
        self.progress_bar = ttk.Progressbar(
            self.status_bar, 
            mode='indeterminate',
            length=200
        )
        
        # Statistics labels
        self.stats_frame = ttk.Frame(self.status_bar)
        self.stats_frame.pack(side=tk.RIGHT)
        
        self.projects_label = ttk.Label(self.stats_frame, text="Projects: 0")
        self.projects_label.pack(side=tk.RIGHT, padx=5)
        
        self.backups_label = ttk.Label(self.stats_frame, text="Backups: 0")
        self.backups_label.pack(side=tk.RIGHT, padx=5)
        
        self.monitoring_label = ttk.Label(self.stats_frame, text="●", foreground="red")
        self.monitoring_label.pack(side=tk.RIGHT, padx=5)
    
    def _setup_callbacks(self) -> None:
        """Setup callbacks for version control events."""
        self.version_control.add_change_callback(self._on_file_change)
        self.version_control.add_backup_callback(self._on_backup_created)
    
    def _load_initial_data(self) -> None:
        """Load initial data and setup."""
        # Load projects from settings if available
        projects_dir = settings.get('paths', 'simatic_projects_dir')
        if projects_dir and Path(projects_dir).exists():
            self.scan_directory(Path(projects_dir))
        
        # Start monitoring if enabled
        if settings.get('general', 'startup_scan', True):
            self.monitoring_active.set(True)
            self.toggle_monitoring()
        
        # Update UI
        self.update_status_bar()
    
    def scan_for_projects(self) -> None:
        """Open dialog to scan for projects."""
        directory = filedialog.askdirectory(
            title="Select Directory to Scan for Simatic Projects"
        )
        
        if directory:
            self.scan_directory(Path(directory))
    
    def scan_directory(self, directory: Path) -> None:
        """
        Scan directory for projects in background thread.
        
        Args:
            directory: Directory to scan
        """
        self.set_status("Scanning for projects...", show_progress=True)
        
        def scan_worker():
            try:
                projects = self.version_control.scan_for_projects(directory, recursive=True)
                
                # Update UI in main thread
                self.root.after(0, lambda: self._on_scan_complete(projects))
            
            except Exception as e:
                self.logger.error(f"Error scanning directory: {e}")
                self.root.after(0, lambda: self._on_scan_error(str(e)))
        
        threading.Thread(target=scan_worker, daemon=True).start()
    
    def _on_scan_complete(self, projects: list) -> None:
        """Handle scan completion."""
        self.set_status(f"Found {len(projects)} projects", show_progress=False)
        
        if projects:
            # Show dialog to select projects to add
            self._show_project_selection_dialog(projects)
        else:
            messagebox.showinfo("Scan Complete", "No Simatic projects found in the selected directory.")
        
        self.update_status_bar()
    
    def _on_scan_error(self, error_message: str) -> None:
        """Handle scan error."""
        self.set_status("Scan failed", show_progress=False)
        messagebox.showerror("Scan Error", f"Error scanning directory:\n{error_message}")
    
    def _show_project_selection_dialog(self, projects: list) -> None:
        """Show dialog to select which projects to add."""
        # This would be implemented as a separate dialog
        # For now, add all projects
        for project in projects:
            self.version_control.add_project(project)
        
        if self.project_manager:
            self.project_manager.refresh()
    
    def add_project_dialog(self) -> None:
        """Show dialog to manually add a project."""
        file_path = filedialog.askopenfilename(
            title="Select Simatic Project File",
            filetypes=[
                ("STEP 7 Projects", "*.s7p"),
                ("TIA Portal Projects", "*.ap15;*.ap16;*.ap17;*.ap18"),
                ("STEP 5 Projects", "*.s5d"),
                ("All Files", "*.*")
            ]
        )
        
        if file_path:
            # Create project from file
            project_file = Path(file_path)
            project = SimaticProject(
                name=project_file.stem,
                path=project_file.parent,
                project_file=project_file,
                project_type="STEP7",  # Default, would be detected properly
                last_modified=datetime.fromtimestamp(project_file.stat().st_mtime)
            )
            
            self.version_control.add_project(project)
            
            if self.project_manager:
                self.project_manager.refresh()
            
            self.update_status_bar()
    
    def toggle_monitoring(self) -> None:
        """Toggle file system monitoring."""
        if self.monitoring_active.get():
            try:
                self.version_control.start_monitoring()
                self.set_status("File monitoring started")
                self.monitoring_label.config(foreground="green")
            except Exception as e:
                self.logger.error(f"Error starting monitoring: {e}")
                messagebox.showerror("Monitoring Error", f"Failed to start monitoring:\n{e}")
                self.monitoring_active.set(False)
        else:
            self.version_control.stop_monitoring()
            self.set_status("File monitoring stopped")
            self.monitoring_label.config(foreground="red")
    
    def toggle_auto_backup(self) -> None:
        """Toggle automatic backup."""
        enabled = self.auto_backup_enabled.get()
        settings.set('general', 'auto_backup', enabled)
        settings.save_settings()
        
        self.version_control.auto_backup_enabled = enabled
        
        status = "enabled" if enabled else "disabled"
        self.set_status(f"Auto backup {status}")
    
    def refresh_projects(self) -> None:
        """Refresh project list and status."""
        if self.project_manager:
            self.project_manager.refresh()
        
        if self.backup_scheduler:
            self.backup_scheduler.refresh()
        
        self.update_status_bar()
        self.set_status("Projects refreshed")
    
    def show_settings(self) -> None:
        """Show settings dialog."""
        dialog = SettingsDialog(self.root)
        if dialog.result:
            # Settings were changed, refresh UI
            self.refresh_projects()
    
    def show_backup_scheduler(self) -> None:
        """Show backup scheduler tab."""
        self.notebook.select(1)  # Select backup tab
    
    def cleanup_backups(self) -> None:
        """Show cleanup backups dialog."""
        # This would be implemented as a separate dialog
        messagebox.showinfo("Cleanup", "Backup cleanup functionality would be implemented here.")
    
    def clear_recent_events(self) -> None:
        """Clear recent file change events."""
        self.version_control.file_monitor.clear_recent_events()
        self.set_status("Recent events cleared")
    
    def show_statistics(self) -> None:
        """Show application statistics."""
        stats = self.version_control.get_statistics()
        
        message = f"""SIMAVER Statistics:

Monitored Projects: {stats['monitored_projects']}
Total Backups: {stats['total_backups']}
Total Backup Size: {stats['total_backup_size'] / (1024*1024):.1f} MB
Changes Tracked: {stats['total_changes_tracked']}
Monitoring Active: {'Yes' if stats['monitoring_active'] else 'No'}
Auto Backup: {'Enabled' if stats['auto_backup_enabled'] else 'Disabled'}"""
        
        messagebox.showinfo("Statistics", message)
    
    def show_about(self) -> None:
        """Show about dialog."""
        about_text = """SIMAVER - Simatic Manager Version Control
Version 1.0

A desktop version control system for Simatic Manager PLC projects.

Features:
• Automatic project detection and monitoring
• Version control with backup and restore
• File change tracking and comparison
• Scheduled backups
• Windows system integration

Developed with Python and Tkinter."""
        
        messagebox.showinfo("About SIMAVER", about_text)
    
    def show_user_guide(self) -> None:
        """Show user guide."""
        messagebox.showinfo("User Guide", "User guide would be implemented here.")
    
    def import_settings(self) -> None:
        """Import settings from file."""
        messagebox.showinfo("Import", "Settings import would be implemented here.")
    
    def export_settings(self) -> None:
        """Export settings to file."""
        messagebox.showinfo("Export", "Settings export would be implemented here.")
    
    def set_status(self, message: str, show_progress: bool = False) -> None:
        """
        Set status bar message.
        
        Args:
            message: Status message to display
            show_progress: Whether to show progress bar
        """
        self.status_label.config(text=message)
        
        if show_progress:
            self.progress_bar.pack(side=tk.LEFT, padx=10)
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
            self.progress_bar.pack_forget()
    
    def update_status_bar(self) -> None:
        """Update status bar with current statistics."""
        stats = self.version_control.get_statistics()
        
        self.projects_label.config(text=f"Projects: {stats['monitored_projects']}")
        self.backups_label.config(text=f"Backups: {stats['total_backups']}")
    
    def _on_file_change(self, project_name: str, event) -> None:
        """Handle file change events."""
        # Update UI in main thread
        self.root.after(0, lambda: self._update_on_change(project_name, event))
    
    def _update_on_change(self, project_name: str, event) -> None:
        """Update UI after file change."""
        if self.project_manager:
            self.project_manager.on_project_changed(project_name)
        
        self.set_status(f"File changed in {project_name}: {Path(event.file_path).name}")
    
    def _on_backup_created(self, project_name: str, backup_info) -> None:
        """Handle backup creation events."""
        # Update UI in main thread
        self.root.after(0, lambda: self._update_on_backup(project_name, backup_info))
    
    def _update_on_backup(self, project_name: str, backup_info) -> None:
        """Update UI after backup creation."""
        if self.project_manager:
            self.project_manager.on_backup_created(project_name)
        
        if self.backup_scheduler:
            self.backup_scheduler.refresh()
        
        self.update_status_bar()
        self.set_status(f"Backup created for {project_name}")
    
    def on_closing(self) -> None:
        """Handle window closing event."""
        # Save window position and size
        geometry = self.root.geometry()
        width, height, x, y = map(int, geometry.replace('x', '+').replace('+', ' ').split())
        
        settings.set('ui', 'window_width', width)
        settings.set('ui', 'window_height', height)
        settings.set('ui', 'window_x', x)
        settings.set('ui', 'window_y', y)
        settings.save_settings()
        
        # Shutdown version control
        self.version_control.shutdown()
        
        # Close window
        self.root.destroy()
    
    def run(self) -> None:
        """Start the application main loop."""
        self.logger.info("Starting SIMAVER application")
        self.root.mainloop()
