"""
Project manager UI for SIMAVER application.

This module provides the project management interface with project list,
status display, and project operations.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
import logging
from pathlib import Path
from typing import Optional, Dict, List
from datetime import datetime

from logic.version_control import VersionControl, ProjectStatus
from logic.backup_manager import BackupInfo
from .diff_viewer import DiffViewerDialog


class ProjectManagerFrame(ttk.Frame):
    """
    Project management interface.
    
    Provides a comprehensive view of monitored projects with status,
    backup operations, and project management functions.
    """
    
    def __init__(self, parent, version_control: VersionControl):
        """
        Initialize project manager frame.
        
        Args:
            parent: Parent widget
            version_control: VersionControl instance
        """
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.version_control = version_control
        
        # Setup UI
        self._setup_ui()
        self._setup_context_menu()
        
        # Load initial data
        self.refresh()
    
    def _setup_ui(self) -> None:
        """Setup the user interface."""
        # Configure grid
        self.columnconfigure(0, weight=1)
        self.rowconfigure(1, weight=1)
        
        # Create toolbar
        self._create_toolbar()
        
        # Create project list
        self._create_project_list()
        
        # Create details panel
        self._create_details_panel()
    
    def _create_toolbar(self) -> None:
        """Create project toolbar."""
        toolbar = ttk.Frame(self)
        toolbar.grid(row=0, column=0, sticky="ew", padx=5, pady=5)
        
        # Add project button
        ttk.Button(
            toolbar,
            text="Add Project",
            command=self.add_project
        ).pack(side=tk.LEFT, padx=2)
        
        # Remove project button
        ttk.Button(
            toolbar,
            text="Remove Project",
            command=self.remove_project
        ).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Create backup button
        ttk.Button(
            toolbar,
            text="Create Backup",
            command=self.create_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Restore backup button
        ttk.Button(
            toolbar,
            text="Restore Backup",
            command=self.restore_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Compare button
        ttk.Button(
            toolbar,
            text="Compare",
            command=self.compare_with_backup
        ).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Refresh button
        ttk.Button(
            toolbar,
            text="Refresh",
            command=self.refresh
        ).pack(side=tk.RIGHT, padx=2)
    
    def _create_project_list(self) -> None:
        """Create project list with treeview."""
        # Create frame for project list
        list_frame = ttk.LabelFrame(self, text="Projects")
        list_frame.grid(row=1, column=0, sticky="nsew", padx=5, pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Create treeview
        columns = ("name", "type", "path", "status", "changes", "last_backup")
        self.project_tree = ttk.Treeview(list_frame, columns=columns, show="headings")
        
        # Configure columns
        self.project_tree.heading("name", text="Project Name")
        self.project_tree.heading("type", text="Type")
        self.project_tree.heading("path", text="Path")
        self.project_tree.heading("status", text="Status")
        self.project_tree.heading("changes", text="Changes")
        self.project_tree.heading("last_backup", text="Last Backup")
        
        # Configure column widths
        self.project_tree.column("name", width=200)
        self.project_tree.column("type", width=100)
        self.project_tree.column("path", width=300)
        self.project_tree.column("status", width=100)
        self.project_tree.column("changes", width=80)
        self.project_tree.column("last_backup", width=150)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.project_tree.yview)
        h_scrollbar = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.project_tree.xview)
        self.project_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid layout
        self.project_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        # Bind events
        self.project_tree.bind("<<TreeviewSelect>>", self.on_project_select)
        self.project_tree.bind("<Double-1>", self.on_project_double_click)
        self.project_tree.bind("<Button-3>", self.on_project_right_click)
    
    def _create_details_panel(self) -> None:
        """Create project details panel."""
        # Create frame for details
        details_frame = ttk.LabelFrame(self, text="Project Details")
        details_frame.grid(row=2, column=0, sticky="ew", padx=5, pady=5)
        details_frame.columnconfigure(1, weight=1)
        
        # Project info
        ttk.Label(details_frame, text="Name:").grid(row=0, column=0, sticky="w", padx=5, pady=2)
        self.name_label = ttk.Label(details_frame, text="")
        self.name_label.grid(row=0, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Type:").grid(row=1, column=0, sticky="w", padx=5, pady=2)
        self.type_label = ttk.Label(details_frame, text="")
        self.type_label.grid(row=1, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Path:").grid(row=2, column=0, sticky="w", padx=5, pady=2)
        self.path_label = ttk.Label(details_frame, text="")
        self.path_label.grid(row=2, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Status:").grid(row=3, column=0, sticky="w", padx=5, pady=2)
        self.status_label = ttk.Label(details_frame, text="")
        self.status_label.grid(row=3, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Changes:").grid(row=4, column=0, sticky="w", padx=5, pady=2)
        self.changes_label = ttk.Label(details_frame, text="")
        self.changes_label.grid(row=4, column=1, sticky="w", padx=5, pady=2)
        
        ttk.Label(details_frame, text="Last Backup:").grid(row=5, column=0, sticky="w", padx=5, pady=2)
        self.backup_label = ttk.Label(details_frame, text="")
        self.backup_label.grid(row=5, column=1, sticky="w", padx=5, pady=2)
    
    def _setup_context_menu(self) -> None:
        """Setup context menu for project list."""
        self.context_menu = tk.Menu(self, tearoff=0)
        self.context_menu.add_command(label="Create Backup", command=self.create_backup)
        self.context_menu.add_command(label="Restore Backup", command=self.restore_backup)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Compare with Backup", command=self.compare_with_backup)
        self.context_menu.add_command(label="View Backup History", command=self.view_backup_history)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Open Project Folder", command=self.open_project_folder)
        self.context_menu.add_command(label="Project Properties", command=self.show_project_properties)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="Remove Project", command=self.remove_project)
    
    def refresh(self) -> None:
        """Refresh project list."""
        # Clear existing items
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)
        
        # Get project statuses
        statuses = self.version_control.get_all_project_statuses()
        
        # Populate tree
        for project_name, status in statuses.items():
            self._add_project_to_tree(status)
        
        self.logger.debug(f"Refreshed project list with {len(statuses)} projects")
    
    def _add_project_to_tree(self, status: ProjectStatus) -> None:
        """Add project to tree view."""
        project = status.project
        
        # Format status
        if status.monitoring_active:
            status_text = "Monitoring"
        else:
            status_text = "Not Monitored"
        
        # Format changes
        if status.has_changes:
            changes_text = f"{status.change_count} changes"
        else:
            changes_text = "No changes"
        
        # Format last backup
        if status.last_backup:
            backup_text = status.last_backup.timestamp.strftime("%Y-%m-%d %H:%M")
        else:
            backup_text = "No backup"
        
        # Insert into tree
        item = self.project_tree.insert("", tk.END, values=(
            project.name,
            project.project_type,
            str(project.path),
            status_text,
            changes_text,
            backup_text
        ))
        
        # Store project name as item tag
        self.project_tree.set(item, "project_name", project.name)
    
    def on_project_select(self, event) -> None:
        """Handle project selection."""
        selection = self.project_tree.selection()
        if not selection:
            self._clear_details()
            return
        
        item = selection[0]
        values = self.project_tree.item(item, "values")
        project_name = values[0]
        
        # Get project status
        status = self.version_control.get_project_status(project_name)
        if status:
            self._update_details(status)
    
    def on_project_double_click(self, event) -> None:
        """Handle project double-click."""
        self.show_project_properties()
    
    def on_project_right_click(self, event) -> None:
        """Handle project right-click."""
        # Select item under cursor
        item = self.project_tree.identify_row(event.y)
        if item:
            self.project_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def _update_details(self, status: ProjectStatus) -> None:
        """Update details panel with project status."""
        project = status.project
        
        self.name_label.config(text=project.name)
        self.type_label.config(text=project.project_type)
        self.path_label.config(text=str(project.path))
        
        # Status with color coding
        if status.monitoring_active:
            self.status_label.config(text="Monitoring", foreground="green")
        else:
            self.status_label.config(text="Not Monitored", foreground="red")
        
        # Changes with color coding
        if status.has_changes:
            self.changes_label.config(
                text=f"{status.change_count} changes", 
                foreground="orange"
            )
        else:
            self.changes_label.config(text="No changes", foreground="green")
        
        # Last backup
        if status.last_backup:
            backup_text = status.last_backup.timestamp.strftime("%Y-%m-%d %H:%M:%S")
            self.backup_label.config(text=backup_text)
        else:
            self.backup_label.config(text="No backup")
    
    def _clear_details(self) -> None:
        """Clear details panel."""
        self.name_label.config(text="")
        self.type_label.config(text="")
        self.path_label.config(text="")
        self.status_label.config(text="")
        self.changes_label.config(text="")
        self.backup_label.config(text="")
    
    def _get_selected_project(self) -> Optional[str]:
        """Get currently selected project name."""
        selection = self.project_tree.selection()
        if not selection:
            return None
        
        item = selection[0]
        values = self.project_tree.item(item, "values")
        return values[0]
    
    def add_project(self) -> None:
        """Add new project."""
        file_path = filedialog.askopenfilename(
            title="Select Simatic Project File",
            filetypes=[
                ("STEP 7 Projects", "*.s7p"),
                ("TIA Portal Projects", "*.ap15;*.ap16;*.ap17;*.ap18"),
                ("STEP 5 Projects", "*.s5d"),
                ("All Files", "*.*")
            ]
        )
        
        if file_path:
            # This would be implemented to create and add project
            messagebox.showinfo("Add Project", f"Would add project: {file_path}")
            self.refresh()
    
    def remove_project(self) -> None:
        """Remove selected project."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project to remove.")
            return
        
        result = messagebox.askyesno(
            "Remove Project",
            f"Remove project '{project_name}' from monitoring?\n\n"
            "This will not delete the project files, only stop monitoring them."
        )
        
        if result:
            self.version_control.remove_project(project_name)
            self.refresh()
    
    def create_backup(self) -> None:
        """Create backup for selected project."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project to backup.")
            return
        
        # Show backup dialog (simplified)
        description = simpledialog.askstring(
            "Create Backup",
            f"Enter description for backup of '{project_name}':",
            initialvalue="Manual backup"
        )
        
        if description is not None:  # User didn't cancel
            backup_info = self.version_control.create_backup(project_name, description)
            if backup_info:
                messagebox.showinfo("Backup Created", f"Backup created: {backup_info.backup_id}")
                self.refresh()
            else:
                messagebox.showerror("Backup Failed", "Failed to create backup.")
    
    def restore_backup(self) -> None:
        """Restore backup for selected project."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project.")
            return
        
        # Get available backups
        backups = self.version_control.backup_manager.list_backups(project_name)
        if not backups:
            messagebox.showinfo("No Backups", f"No backups found for project '{project_name}'.")
            return
        
        # Show backup selection dialog (simplified)
        backup_names = [f"{b.backup_id} - {b.timestamp.strftime('%Y-%m-%d %H:%M')}" 
                       for b in backups]
        
        # For now, just restore the latest backup
        result = messagebox.askyesno(
            "Restore Backup",
            f"Restore latest backup for '{project_name}'?\n\n"
            f"Latest backup: {backups[0].timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n"
            "This will overwrite current project files."
        )
        
        if result:
            success = self.version_control.restore_backup(backups[0])
            if success:
                messagebox.showinfo("Restore Complete", "Backup restored successfully.")
                self.refresh()
            else:
                messagebox.showerror("Restore Failed", "Failed to restore backup.")
    
    def compare_with_backup(self) -> None:
        """Compare project with backup."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project.")
            return
        
        # Get available backups
        backups = self.version_control.backup_manager.list_backups(project_name)
        if not backups:
            messagebox.showinfo("No Backups", f"No backups found for project '{project_name}'.")
            return
        
        # For now, compare with latest backup
        diff = self.version_control.compare_with_backup(project_name, backups[0])
        if diff:
            # Show diff viewer dialog
            DiffViewerDialog(self, project_name, backups[0], diff)
        else:
            messagebox.showerror("Compare Failed", "Failed to compare with backup.")
    
    def view_backup_history(self) -> None:
        """View backup history for selected project."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project.")
            return
        
        # This would show a detailed backup history dialog
        messagebox.showinfo("Backup History", f"Backup history for '{project_name}' would be shown here.")
    
    def open_project_folder(self) -> None:
        """Open project folder in file explorer."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project.")
            return
        
        status = self.version_control.get_project_status(project_name)
        if status:
            import subprocess
            subprocess.Popen(f'explorer "{status.project.path}"')
    
    def show_project_properties(self) -> None:
        """Show project properties dialog."""
        project_name = self._get_selected_project()
        if not project_name:
            messagebox.showwarning("No Selection", "Please select a project.")
            return
        
        # This would show a detailed properties dialog
        messagebox.showinfo("Project Properties", f"Properties for '{project_name}' would be shown here.")
    
    def on_project_changed(self, project_name: str) -> None:
        """Handle project change notification."""
        # Update the specific project in the tree
        for item in self.project_tree.get_children():
            values = self.project_tree.item(item, "values")
            if values[0] == project_name:
                # Refresh this item
                status = self.version_control.get_project_status(project_name)
                if status:
                    # Update changes column
                    if status.has_changes:
                        changes_text = f"{status.change_count} changes"
                    else:
                        changes_text = "No changes"
                    
                    # Update the item
                    current_values = list(values)
                    current_values[4] = changes_text  # Changes column
                    self.project_tree.item(item, values=current_values)
                break
        
        # Update details if this project is selected
        selected_project = self._get_selected_project()
        if selected_project == project_name:
            status = self.version_control.get_project_status(project_name)
            if status:
                self._update_details(status)
    
    def on_backup_created(self, project_name: str) -> None:
        """Handle backup creation notification."""
        # Refresh the project list to update backup information
        self.refresh()
